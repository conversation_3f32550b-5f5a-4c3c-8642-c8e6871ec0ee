#!/bin/bash

# Environment Setup Script for Impano y'Imana Frontend

echo "🚀 Setting up environment variables for Impano y'Imana Frontend..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "✅ .env file created successfully!"
    else
        echo "❌ .env.example not found. Creating basic .env file..."
        cat > .env << EOF
# API Configuration
VITE_API_BASE_URL=

# Development Configuration
VITE_DEV_MODE=true
VITE_CACHE_BUSTING=true
EOF
        echo "✅ Basic .env file created!"
    fi
else
    echo "✅ .env file already exists"
fi

# Validate environment variables
echo "🔍 Validating environment variables..."

if grep -q "VITE_API_BASE_URL" .env; then
    echo "✅ VITE_API_BASE_URL is configured"
else
    echo "⚠️  VITE_API_BASE_URL is missing from .env"
fi

if grep -q "VITE_DEV_MODE" .env; then
    echo "✅ VITE_DEV_MODE is configured"
else
    echo "⚠️  VITE_DEV_MODE is missing from .env"
fi

if grep -q "VITE_CACHE_BUSTING" .env; then
    echo "✅ VITE_CACHE_BUSTING is configured"
else
    echo "⚠️  VITE_CACHE_BUSTING is missing from .env"
fi

echo ""
echo "🎉 Environment setup complete!"
echo "📖 Check README.md for more information about environment variables"
echo "🚀 Run 'npm run dev' to start the development server"
