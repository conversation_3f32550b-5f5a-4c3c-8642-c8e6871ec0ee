# Impano y'Imana Frontend

This is the Front-End for the Impano y'Imana Ministry website.

## Environment Variables

This project uses environment variables for configuration. Create a `.env` file in the root directory with the following variables:

```bash
# API Configuration
VITE_API_BASE_URL=
```

### Environment Variables Explained

- `VITE_API_BASE_URL`: The base URL for the API endpoints

### Setup Instructions

1. Copy `.env.example` to `.env`
2. Update the values in `.env` according to your environment
3. Run `npm run dev` to start the development server

**Note**: The `.env` file is ignored by git for security reasons. Use `.env.example` as a template.
