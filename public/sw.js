const CACHE_NAME = "impano-y-imana-v2"; // Increment version for updates
const urlsToCache = [
  "/",
  "/articles",
  "/quotes",
  "/manifest.json",
  "/ISEZERANO LOGO copy - rm bg.png",
  "/IMPANO Y'IMANA logo.png",
  // Icons (will be added when generated)
  "/icons/icon-192x192.png",
  "/icons/icon-512x512.png",
];

// Cache for individual article pages
const ARTICLE_CACHE_NAME = "impano-articles-v2";

// Cache for individual quote pages
const QUOTE_CACHE_NAME = "impano-quotes-v2";

// API endpoints to cache
const API_CACHE_NAME = "impano-api-v2";
const apiUrlsToCache = [
  `${import.meta.env.VITE_API_BASE_URL}/blog-posts/`,
  `${import.meta.env.VITE_API_BASE_URL}/blog-posts/?page=1&page_size=6`,
  `${import.meta.env.VITE_API_BASE_URL}/blog-posts/?page=1&page_size=9`,
  `${import.meta.env.VITE_API_BASE_URL}/quotes/`,
  `${import.meta.env.VITE_API_BASE_URL}/quotes/?page=1&page_size=12`,
];

// Install event - cache resources
self.addEventListener("install", (event) => {
  event.waitUntil(
    Promise.all([
      caches.open(CACHE_NAME).then((cache) => cache.addAll(urlsToCache)),
      caches.open(API_CACHE_NAME).then((cache) => cache.addAll(apiUrlsToCache)),
    ]).then(() => {
      // Skip waiting to activate immediately
      return self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches and take control
self.addEventListener("activate", (event) => {
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== ARTICLE_CACHE_NAME &&
              cacheName !== QUOTE_CACHE_NAME &&
              cacheName !== API_CACHE_NAME
            ) {
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // Take control of all clients immediately
      self.clients.claim(),
    ]).then(() => {
      // Only notify clients if this is actually a new version
      // Check if we're replacing an existing service worker
      if (self.registration.active && self.registration.active !== self) {
        return self.clients.matchAll().then((clients) => {
          clients.forEach((client) => {
            client.postMessage({
              type: "UPDATE_AVAILABLE",
              message: "New version available",
            });
          });
        });
      }
    })
  );
});

// Handle messages from clients
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
});

// Fetch event - serve from cache, fallback to network
self.addEventListener("fetch", (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Handle API requests
  if (url.origin === import.meta.env.VITE_API_BASE_URL) {
    event.respondWith(
      caches.open(API_CACHE_NAME).then((cache) => {
        return fetch(request)
          .then((response) => {
            // Cache successful responses
            if (response.status === 200) {
              cache.put(request, response.clone());
            }
            return response;
          })
          .catch(() => {
            // Return cached version if network fails
            return cache.match(request);
          });
      })
    );
    return;
  }

  // Handle article pages
  if (url.pathname.startsWith("/article/")) {
    event.respondWith(
      caches.open(ARTICLE_CACHE_NAME).then((cache) => {
        return cache.match(request).then((response) => {
          if (response) {
            // Serve from cache and update in background
            fetch(request)
              .then((networkResponse) => {
                if (networkResponse.status === 200) {
                  cache.put(request, networkResponse.clone());
                }
              })
              .catch(() => {
                // Network failed, but we have cache
              });
            return response;
          }

          // Not in cache, fetch from network
          return fetch(request).then((networkResponse) => {
            if (networkResponse.status === 200) {
              cache.put(request, networkResponse.clone());
            }
            return networkResponse;
          });
        });
      })
    );
    return;
  }

  // Handle quote pages
  if (url.pathname.startsWith("/quotes/")) {
    event.respondWith(
      caches.open(QUOTE_CACHE_NAME).then((cache) => {
        return cache.match(request).then((response) => {
          if (response) {
            // Serve from cache and update in background
            fetch(request)
              .then((networkResponse) => {
                if (networkResponse.status === 200) {
                  cache.put(request, networkResponse.clone());
                }
              })
              .catch(() => {
                // Network failed, but we have cache
              });
            return response;
          }

          // Not in cache, fetch from network
          return fetch(request).then((networkResponse) => {
            if (networkResponse.status === 200) {
              cache.put(request, networkResponse.clone());
            }
            return networkResponse;
          });
        });
      })
    );
    return;
  }

  // Handle other requests
  event.respondWith(
    caches.match(request).then((response) => {
      if (response) {
        return response;
      }

      return fetch(request).then((response) => {
        // Don't cache non-successful responses
        if (!response || response.status !== 200 || response.type !== "basic") {
          return response;
        }

        // Clone the response
        const responseToCache = response.clone();

        caches.open(CACHE_NAME).then((cache) => {
          cache.put(request, responseToCache);
        });

        return response;
      });
    })
  );
});

// Push notification support
self.addEventListener("push", (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: "/icons/icon-192x192.png",
      badge: "/icons/icon-72x72.png",
      vibrate: [100, 50, 100],
      data: {
        dateOfArrival: Date.now(),
        primaryKey: 1,
      },
    };

    event.waitUntil(self.registration.showNotification(data.title, options));
  }
});

// Notification click handler
self.addEventListener("notificationclick", (event) => {
  event.notification.close();

  event.waitUntil(clients.openWindow("/"));
});
