# PWA Icons

This directory should contain the following icon sizes for PWA functionality:

## Required Icon Sizes:
- icon-72x72.png
- icon-96x96.png  
- icon-128x128.png
- icon-144x144.png
- icon-152x152.png
- icon-192x192.png
- icon-384x384.png
- icon-512x512.png

## How to Generate Icons:

1. **Use your existing logo**: `/ISEZERANO LOGO copy - rm bg.png`
2. **Online tools**: 
   - https://realfavicongenerator.net/
   - https://www.favicon-generator.org/
   - https://favicon.io/

3. **Manual resize**: Use any image editor to create these sizes

## Icon Requirements:
- **Format**: PNG
- **Background**: Should work on any background (transparent or solid)
- **Design**: Simple, recognizable at small sizes
- **Colors**: Match your brand colors (#B61B3D, #F1F1F1)

## Current Status:
⚠️ **Icons need to be generated and placed in this directory**

The PWA will work without icons, but users will see generic icons instead of your branded ones.
