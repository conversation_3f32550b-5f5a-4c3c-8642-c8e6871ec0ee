{"name": "impano-y-imana-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "clean": "rm -rf node_modules/.vite dist .vite", "setup": "chmod +x scripts/setup-env.sh && ./scripts/setup-env.sh"}, "dependencies": {"@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@vercel/analytics": "^1.5.0", "axios": "^1.7.9", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.28.0"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/node": "^22.14.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^7.0.6"}}