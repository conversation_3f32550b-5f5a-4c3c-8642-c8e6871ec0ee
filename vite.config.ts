import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: parseInt(process.env.PORT || '3000'),
    host: true,
    hmr: {
      overlay: true,
      port: 24678
    }
  },
  // Add this for production build optimization
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: undefined
      }
    }
  },
  // Fix dependency optimization issues
  optimizeDeps: {
    force: true
  },
  // Clear cache on restart
  clearScreen: true,
  // Add cache busting
  define: {
    __BUILD_TIME__: JSON.stringify(Date.now())
  }
})
