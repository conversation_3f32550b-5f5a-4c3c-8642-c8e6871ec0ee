/* src/NavBar.css */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 32px;
    background-color: #B61B3D;
    /* Primary color */
    color: white;
}

.navbar-logo {
    font-size: 1.5em;
    font-weight: bold;
    text-decoration: none;
    color: white;
}

.navbar-links {
    list-style: none;
    display: flex;
    gap: 32px;
    margin: 0;
    padding: 0;
}

.navbar-links li {
    text-decoration: none;
}

.navbar-links a {
    text-decoration: none;
    color: white;
    font-size: 1em;
    transition: color 0.3s;
}

.navbar-links a:hover {
    color: #ffdddd;
    /* A lighter shade of the primary color for hover effect */
}

nav ul {
    transition: transform 0.3s ease-in-out;
}

nav button div {
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}