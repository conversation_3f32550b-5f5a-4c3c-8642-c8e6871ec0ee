/* src/Pagination.css */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    margin: 20px 0;
}

.pagination-btn,
.pagination-number {
    padding: 5px 10px;
    border: 1px solid #ccc;
    background-color: #fff;
    color: #B61B3D;
    /* Primary color for text */
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s, color 0.3s;
}

.pagination-number.active {
    background-color: #B61B3D;
    /* Primary color for active page */
    color: white;
    border: 1px solid #B61B3D;
}

.pagination-btn:disabled {
    background-color: #e9ecef;
    color: #ccc;
    cursor: not-allowed;
}

.pagination-number:hover:not(.active),
.pagination-btn:not(:disabled):hover {
    background-color: #B61B3D;
    /* Primary color for hover effect */
    color: white;
}

hr {
    border: 1px solid #B61B3D;
    background-color: #B61B3D;
}