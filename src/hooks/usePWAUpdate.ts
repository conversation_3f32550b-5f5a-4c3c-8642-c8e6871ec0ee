import { useState, useEffect } from 'react';

interface PWAUpdateState {
  updateAvailable: boolean;
  showUpdatePrompt: boolean;
  isUpdating: boolean;
}

export const usePWAUpdate = () => {
  const [updateState, setUpdateState] = useState<PWAUpdateState>({
    updateAvailable: false,
    showUpdatePrompt: false,
    isUpdating: false
  });

  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    let isCheckingForUpdates = false;

    // Register service worker and listen for updates
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((reg) => {
          setRegistration(reg);

          // Listen for updates
          reg.addEventListener('updatefound', () => {
            const newWorker = reg.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available
                  setUpdateState(prev => ({
                    ...prev,
                    updateAvailable: true,
                    showUpdatePrompt: true
                  }));
                }
              });
            }
          });

          // Check for updates less frequently and with debouncing
          const checkForUpdates = async () => {
            if (document.visibilityState === 'visible' && !isCheckingForUpdates) {
              isCheckingForUpdates = true;
              try {
                await reg.update();
              } catch (error) {
                console.error('Update check failed:', error);
              } finally {
                // Add delay before allowing next update check
                setTimeout(() => {
                  isCheckingForUpdates = false;
                }, 5000);
              }
            }
          };

          // Check for updates every 5 minutes instead of 30 seconds
          interval = setInterval(checkForUpdates, 5 * 60 * 1000);

          // Check once when visibility changes, but with debouncing
          let visibilityTimeout: NodeJS.Timeout;
          const handleVisibilityChange = () => {
            clearTimeout(visibilityTimeout);
            visibilityTimeout = setTimeout(checkForUpdates, 1000);
          };

          document.addEventListener('visibilitychange', handleVisibilityChange);

          return () => {
            clearInterval(interval);
            clearTimeout(visibilityTimeout);
            document.removeEventListener('visibilitychange', handleVisibilityChange);
          };
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });

      // Listen for messages from service worker
      const handleMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
          setUpdateState(prev => ({
            ...prev,
            updateAvailable: true,
            showUpdatePrompt: true
          }));
        }
      };

      navigator.serviceWorker.addEventListener('message', handleMessage);

      return () => {
        navigator.serviceWorker.removeEventListener('message', handleMessage);
        clearInterval(interval);
      };
    }
  }, []);

  const applyUpdate = async () => {
    if (!registration || !registration.waiting) {
      console.warn('No service worker registration or waiting worker available');
      return;
    }

    setUpdateState(prev => ({ ...prev, isUpdating: true }));

    try {
      // Tell the waiting service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });

      // Wait for the new service worker to take control with timeout
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
          reject(new Error('Update timeout'));
        }, 10000); // 10 second timeout

        const handleControllerChange = () => {
          clearTimeout(timeout);
          navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
          resolve();
        };

        navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
      });

      // Small delay before reload to ensure everything is ready
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error('Failed to apply update:', error);
      setUpdateState(prev => ({
        ...prev,
        isUpdating: false,
        showUpdatePrompt: false // Hide the prompt on error
      }));

      // Show user-friendly error message
      alert('Habayeho ikibazo mu guvugurura. Gerageza ukongera.');
    }
  };

  const dismissUpdate = () => {
    setUpdateState(prev => ({
      ...prev,
      showUpdatePrompt: false
    }));
  };

  return {
    ...updateState,
    applyUpdate,
    dismissUpdate
  };
};