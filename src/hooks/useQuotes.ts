import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { fetchQuotes, fetchSingleQuote, fetchRelatedQuotes } from '../interfaces/quotes';

// Query keys for better cache management
export const quoteKeys = {
  all: ['quotes'] as const,
  lists: () => [...quoteKeys.all, 'list'] as const,
  list: (filters: any) => [...quoteKeys.lists(), filters] as const,
  details: () => [...quoteKeys.all, 'detail'] as const,
  detail: (id: string) => [...quoteKeys.details(), id] as const,
  related: (categories: string[], excludeId?: number) =>
    [...quoteKeys.all, 'related', categories, excludeId] as const,
};

// Hook to fetch all quotes with optional filters
export const useQuotes = (filters?: {
  page?: number;
  page_size?: number;
  category?: string;
  author?: string;
}) => {
  return useQuery({
    queryKey: quoteKeys.list(filters || {}),
    queryFn: () => fetchQuotes(filters || {}),
  });
};

// Hook to fetch quotes with infinite scrolling
export const useInfiniteQuotes = (pageSize: number = 12) => {
  return useInfiniteQuery({
    queryKey: quoteKeys.lists(),
    queryFn: ({ pageParam = 1 }: { pageParam?: number }) =>
      fetchQuotes({
        page: pageParam,
        page_size: pageSize,
      }),
    getNextPageParam: (lastPage: any, allPages) => {
      return lastPage && lastPage.next ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
  });
};

// Hook to fetch a single quote
export const useQuote = (id: string) => {
  return useQuery({
    queryKey: quoteKeys.detail(id),
    queryFn: () => fetchSingleQuote(id),
    enabled: !!id,
  });
};

// Hook to fetch related quotes
export const useRelatedQuotes = (categories: string[], excludeId?: number) => {
  return useQuery({
    queryKey: quoteKeys.related(categories, excludeId),
    queryFn: () => fetchRelatedQuotes(categories, excludeId),
    enabled: categories.length > 0,
  });
};

// Hook to get unique authors and categories from quotes
export const useQuotesMetadata = () => {
  const { data: quotesData } = useQuotes({ page_size: 100 }); // Get more quotes for metadata

  const authors = quotesData?.results
    ? [...new Set(quotesData.results.map(quote => quote.author))]
    : [];

  const categories = quotesData?.results
    ? [...new Set(
      quotesData.results.flatMap(quote =>
        quote.categories.map(cat => cat.name)
      )
    )]
    : [];

  return {
    authors,
    categories,
    isLoading: !quotesData,
  };
};
