import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';

const OfflineIndicator: React.FC = () => {
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setShowOfflineMessage(true);
      // Hide message after 5 seconds
      setTimeout(() => setShowOfflineMessage(false), 5000);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!showOfflineMessage) {
    return null;
  }

  return (
    <div className="fixed top-4 left-4 right-4 bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded-lg shadow-lg z-50 max-w-sm mx-auto">
      <div className="flex items-center space-x-3">
        <FaExclamationTriangle className="text-yellow-600" />
        <div>
          <p className="font-semibold text-sm">Nta interineti</p>
          <p className="text-xs">Ushobora gusoma inyandiko zasobanuwe mbere</p>
        </div>
      </div>
    </div>
  );
};

export default OfflineIndicator;
