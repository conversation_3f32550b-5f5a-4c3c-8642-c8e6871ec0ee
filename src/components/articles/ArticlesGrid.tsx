import { Link } from "react-router-dom";
import Skeleton from "react-loading-skeleton";
import { Article, Author } from "../../interfaces/Article";

interface ArticlesGridProps {
  articles: Article[];
  loading: boolean;
}

function ArticlesGrid({ articles, loading }: ArticlesGridProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 pt-8 px-4 sm:px-8 md:px-16 lg:px-28">
      {loading
        ? Array.from({ length: 9 }).map((_, index) => (
          <div
            key={index}
            className="rounded-md overflow-hidden bg-white shadow-md p-4"
          >
            <Skeleton height={200} />
            <div className="py-4">
              <Skeleton width="60%" height={20} />
              <Skeleton width="80%" height={28} className="my-3" />
              <div className="flex items-center gap-4 mt-4">
                <Skeleton circle height={32} width={32} />
                <Skeleton width="40%" height={20} />
              </div>
            </div>
          </div>
        ))
        : articles.map((rowItem) => (
          <Link
            to={`/article/${rowItem.slug}`}
            className="rounded-md overflow-hidden bg-white shadow-md hover:shadow-lg transition-shadow duration-300"
            key={rowItem.id}
          >
            <img src={rowItem.featured_image} alt={rowItem.title} />
            <div className="px-4 py-4">
              <h1 className="my-3 sm:my-4 md:my-5 font-bold text-lg sm:text-xl md:text-2xl">
                {rowItem.title}
              </h1>
              <p className="my-3 sm:my-4 md:my-5 text-sm sm:text-base line-clamp-2">
                {rowItem.short_description}
              </p>
              <div className="flex gap-3 sm:gap-4 md:gap-5 items-center">
                {rowItem.authors.map((author: Author) => (
                  <div key={author.id} className="flex items-center">
                    <img
                      className="rounded-full h-6 w-6 sm:h-8 sm:w-8"
                      src={author.profile_picture}
                      alt={author.name}
                    />
                    <p className="ml-2 text-sm sm:text-base">{author.name}</p>
                  </div>
                ))}
              </div>
            </div>
          </Link>
        ))}
    </div>
  );
}

export default ArticlesGrid;
