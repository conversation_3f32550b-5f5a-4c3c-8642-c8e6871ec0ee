import React, { useState } from "react";
import { Article, Author } from "../../interfaces/Article"; 

interface SearchBarProps {
  articles: Article[]; 
  onSearch: (filteredArticles: Article[]) => void; 
}

function SearchBar({ articles, onSearch }: SearchBarProps) {
  const [searchQuery, setSearchQuery] = useState(""); 

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value.toLowerCase(); 
    setSearchQuery(query); 

    
    const filteredArticles = articles.filter(
      (article) =>
        article.title.toLowerCase().includes(query) || 
        article.authors.some(
          (author: Author) => author.name.toLowerCase().includes(query) 
        )
    );

    
    onSearch(filteredArticles);
  };

  return (
    <div className="px-4 sm:px-8 md:px-16 lg:px-28 pt-4">
      <input
        type="text"
        placeholder="Shakisha ukoresheje umutwe w’inkuru cyangwa umwanditsi..."
        value={searchQuery} 
        onChange={handleSearch} 
        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#B61B3D]"
      />
    </div>
  );
}

export default SearchBar;
