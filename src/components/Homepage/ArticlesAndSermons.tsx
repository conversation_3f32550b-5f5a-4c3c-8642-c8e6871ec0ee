import { FaArrowRight } from "react-icons/fa";
import { Link } from "react-router-dom";
import fetchArticles from "../../interfaces/articles";
import { useEffect, useState } from "react";
import { Article, Author } from "../../interfaces/Article";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

function Articles() {

  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getArticles = async () => {
      setLoading(true);
      try {
        // Fetch more articles for homepage display (e.g., 6 articles)
        const data = await fetchArticles({ page: 1, page_size: 6 });
        setArticles(data?.results || []);
      } catch {
        // Handle error silently
      } finally {
        setLoading(false);
      }
    };

    getArticles();
  }, []);

  return (
    <div className="bg-custom-white font-open-sans">
      <div className="flex justify-between px-4 sm:px-8 md:px-16 lg:px-28 text-custom-red pt-6">
        <p className="uppercase">Byinshi</p>
        <Link to={'/articles'} className="flex gap-2 items-center">
          <p>Byose</p>
          <FaArrowRight />
        </Link>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 pt-8 px-4 sm:px-8 md:px-16 lg:px-28">
        {loading
          ? Array.from({ length: 6 }).map((_, index) => (
              <div
                key={index}
                className="rounded-md overflow-hidden bg-white shadow-md p-4"
              >
                <Skeleton height={200} className="rounded-md" />
                <div className="py-4">
                  <Skeleton width="60%" height={16} className="my-2" />
                  <Skeleton width="90%" height={20} className="my-3" />
                  <div className="flex items-center gap-4 mt-4">
                    <Skeleton circle height={32} width={32} />
                    <Skeleton width="40%" height={16} />
                  </div>
                </div>
              </div>
            ))
          : articles.map((rowItem: Article) => (
              <Link to={"/article/" + rowItem.slug} className="rounded-md overflow-hidden bg-white shadow-md hover:shadow-lg transition-shadow duration-300" key={rowItem.id}>
                <img src={rowItem.featured_image} alt={rowItem.title} className="w-full h-48 object-cover" />
                <div className="px-4 py-4">
                  <p
                    id="category"
                    className="text-custom-red font-open-sans font-bold text-[12px] leading-[100%] tracking-[0%] uppercase my-2 sm:my-3 md:my-4"
                  >
                    {rowItem.categories.map((category) => category.name).join(", ")}
                  </p>
                  <h1
                    className="font-open-sans font-bold text-[15px] leading-[100%] tracking-[0%] my-3 sm:my-4 md:my-5 capitalize"
                  >
                    {rowItem.title.toLowerCase()}
                  </h1>
                  <div className="flex gap-3 sm:gap-4 md:gap-5 items-center">
                    {rowItem.authors.map((author: Author) => (
                      <div key={author.id} className="flex items-center">
                        <img className="rounded-full h-6 w-6 sm:h-8 sm:w-8" src={author.profile_picture} alt={author.name} />
                        <p className="ml-2 text-sm sm:text-base">{author.name}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </Link>
            ))}
      </div>
    </div>
  );
}

export default Articles;