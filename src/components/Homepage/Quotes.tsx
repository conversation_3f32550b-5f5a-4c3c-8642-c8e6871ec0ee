
const mockData = [

  { id: 1, image: '/pexels-pixabay-372326.jpg', description: '<PERSON>rem ipsum dolor sit amet consectetur adipisicing elit.', author: '<PERSON>' },
  { id: 1, image: '/pexels-pixabay-372326.jpg', description: 'Lorem ipsum dolor sit amet consectetur adipisicing elit.', author: '<PERSON>' }
]
function Quotes() {
  return (
    <div className="bg-custom-white px-28 pt-8 pb-5">
      <h1 className="text-custom-red uppercase">Quotes</h1>

      <h1 className="text-custom-red text-2xl font-bold font-open-sans">
        Learn from our fellow faithful brothers
      </h1>


      <div className="flex gap-8 pt-8">
        {mockData.map((rowItem) => (
          <div className="flex rounded-md overflow-hidden bg-white shadow-md" key={rowItem.id}>
            <div className="flex-shrink-0 h-80 w-56">
              <img className="h-full w-full object-cover" src={rowItem.image} alt={rowItem.author} />
            </div>
            <div className="flex flex-col">
              <p className="my-5 mx-5 pt-5">{rowItem.description}</p>
              <h1 className="text-custom-red uppercase px-6">{rowItem.author}</h1>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Quotes;
