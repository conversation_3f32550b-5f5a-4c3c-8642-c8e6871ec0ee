import { useEffect, useState } from "react";
import { Article } from "../../interfaces/Article";
import fetchArticles from "../../interfaces/articles";
import { Link } from "react-router-dom";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";

function HeroSection() {
  const [latestArticle, setLatestArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const getLatestArticle = async () => {
      setLoading(true);
      try {
        const data = await fetchArticles({ page: 1, page_size: 1 });
        if (data && data.results && data.results.length > 0) {
          setLatestArticle(data.results[0]); // Set the latest article (first in the list)
        }
      } catch {
        // Handle error silently
      } finally {
        setLoading(false);
      }
    };

    getLatestArticle();
  }, []);

  const defaultArticle: Article = {
    "id": 12,
    "title": "ICYEZA UMUTIMANAMA CYONYINE",
    "original_title": "The Only Conscience Cleanser",
    "slug": "icyeza-umutimanama-cyonyine",
    "short_description": "Nkanswe amaraso ya Kristo witambiye Imana atagira inenge ku bw'Umwuka w'iteka, ntazarushaho guhumanura imitima yanyu akayezaho imirimo ipfuye, kugira ngo mubone uko mukorera Imana ihoraho. (Abaheburayo 9:14)",
    "content": "<p>Nkanswe amaraso ya Kristo witambiye Imana atagira inenge ku bw'Umwuka w'iteka, ntazarushaho guhumanura imitima yanyu akayezaho imirimo ipfuye, kugira ngo mubone uko mukorera Imana ihoraho. (Abaheburayo 9:14)</p><p>&nbsp;</p><p>Turi mu bihe bigezweho - ibihe bya interineti, telefone zigezweho, ingendo zo mu isanzure ry'ikirere n'ubuvuzi buteye imbere nko guhindurira umuntu umutima — ariko kandi ikibazo cyacu kiracyari kimwe nk'ibisanzwe: Umutimanama wacu uduciraho iteka kandi utuma twumva ko tutemewe n'Imana. Dutandukanijwe n'Imana. Umutimanama wacu nawo urabiduhamiriza.</p><p>&nbsp;</p><p>Dushobora kwikebagura, cyangwa kujugunya abana bacu mu ruzi rwera, cyangwa gutanga miliyoni z'amadorari mu gutanga imfashanyo, cyangwa gutanga ibiryo n’ibindi ku batishoboye, cyangwa uburyo ijana bwo kwihana cyangwa kwibabaza, kandi icyo bizana cyizahora ari kimwe: Ikizinga kigumaho kandi urupfu ruzahora ruduteye ubwoba.</p><p>&nbsp;</p><p>Turabizi ko umutimanama wacu wanduye wahumanye — ntabwo ari ibintu byo hanze nko gukora ku murambo, icyahi cy’umwana cyanduye, cyangwa inyama y'ingurube. Yesu yavuze ko ibiva mu muntu aribyo bimuhumanya, atari ibyinjira (Mariko 7: 15-23). Twandujwe n'imyitwarire nk'ubwibone no kwihangayikira gusa no gusharira no kurarikira, ishyari, kwifuza, kutagira icyo twitaho no gutinya.</p><p>&nbsp;</p><p>Igisubizo cyonyine muri iki gihe cya none, kimwe no mu bindi bihe byose, ni amaraso ya Kristo. Iyo umutimanama wawe uhagurutse ukaguciraho iteka, uzahindukirira he? Abaheburayo 9:14 haraguha igisubizo: “nkanswe amaraso ya Kristo witambiye Imana atagira inenge ku bw’Umwuka w’iteka, ntazarushaho guhumanura imitima yanyu akayezaho imirimo ipfuye, kugira ngo mubone uko mukorera Imana ihoraho?”</p><p>&nbsp;</p><p>Igisubizo ni: Hindukirira amaraso ya Kristo. Hindukirira ku cyakweza cyonyine kiri&nbsp;mu isanzure ryose cyaguha ihumure mu buzima, n'amahoro mu rupfu.</p>",
    "featured_image": "https://impano-db.fra1.cdn.digitaloceanspaces.com/impano/media/blog/featured_images/blood_on_cross.webp",
    "categories": [
      {
        "id": 2,
        "name": "Agakiza",
        "slug": "agakiza"
      }
    ],
    "authors": [
      {
        "id": 1,
        "name": "John Piper",
        "bio": "",
        "profile_picture": "https://impano-db.fra1.cdn.digitaloceanspaces.com/impano/media/authors/profile_pictures/johnpiper_profile.jpg"
      }
    ],
    "original_article_url": "https://www.desiringgod.org/articles/the-only-conscience-cleanser",
    "status": "published",
    "created_at": "2025-02-21T11:44:23.042402+02:00",
    "updated_at": "2025-05-06T18:16:49.281241+02:00"
  }

  return (
    <div className="relative h-[60vh] sm:h-[80vh] md:h-screen overflow-hidden">
      {/* Background Image */}
      <img
        src={latestArticle?.featured_image || defaultArticle.featured_image}
        alt={latestArticle?.title || defaultArticle.title}
        className="w-full h-full object-cover"
      />
      <div className="absolute inset-0 bg-gradient-to-b from-black/50 to-black pointer-events-none"></div>

      {/* Content */}
      <div className="absolute bottom-4 sm:bottom-8 md:bottom-1/4 text-custom-white w-full max-w-7xl mx-auto px-4 sm:px-8 md:px-16 font-open-sans flex flex-col md:flex-row items-center md:items-end justify-between gap-4 md:gap-8">
        {loading ? (
          <>
            {/* Loading Article Details */}
            <div id="top-article" className="max-w-prose">
              <Skeleton height={40} width="80%" className="mb-4" baseColor="#ffffff20" highlightColor="#ffffff40" />
              <Skeleton height={20} width="90%" className="mb-2" baseColor="#ffffff20" highlightColor="#ffffff40" />
              <Skeleton height={20} width="70%" className="mb-6" baseColor="#ffffff20" highlightColor="#ffffff40" />
              <Skeleton height={40} width={120} className="rounded-md" baseColor="#ffffff20" highlightColor="#ffffff40" />
            </div>

            {/* Loading Author Details */}
            <div id="author" className="sm:px-3 flex items-center gap-2 sm:gap-4 flex-wrap">
              <div className="flex items-center gap-2 sm:gap-4">
                <Skeleton circle height={48} width={48} baseColor="#ffffff20" highlightColor="#ffffff40" />
                <Skeleton height={20} width={100} baseColor="#ffffff20" highlightColor="#ffffff40" />
              </div>
            </div>
          </>
        ) : (
          <>
            {/* Article Details */}
            <div id="top-article" className="max-w-prose">
              <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold">
                {latestArticle?.title || defaultArticle.title}
              </h1>
              <p className="mt-2 sm:mt-4 text-sm sm:text-base md:text-lg line-clamp-2">
                {latestArticle?.short_description || defaultArticle.short_description}
              </p>
              <Link className="inline-block px-3 py-1 sm:px-4 sm:py-2 border-2 border-custom-white rounded-md mt-4 sm:mt-6 md:mt-8 text-sm sm:text-base hover:text-custom-red hover:border-custom-red transition" to={"/article/" + (latestArticle?.slug || defaultArticle.slug)}>
                <span>Soma byinshi</span>
              </Link>
            </div>

            {/* Author Details */}
            <div id="author" className="sm:px-3 flex items-center gap-2 sm:gap-4 flex-wrap">
              {(
                latestArticle?.authors || defaultArticle.authors
              ).map((author, index) => (
                <div key={index} className="flex items-center gap-2 sm:gap-4">
                  <img
                    src={author.profile_picture}
                    alt={author.name}
                    className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full"
                  />
                  <h3 className="text-sm sm:text-base md:text-lg">{author.name}</h3>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default HeroSection;