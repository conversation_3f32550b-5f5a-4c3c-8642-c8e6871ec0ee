import { Link } from "react-router-dom";
import { useState } from "react";
import "../../styles/navbar.css"; // Optional CSS for styling

const NavBar = () => {
  const [isOpen, setIsOpen] = useState(false); // State for toggling mobile menu

  return (
    <nav className="flex justify-between bg-custom-black px-4 sm:px-8 md:px-12 lg:px-16 h-20 items-center relative">
      {/* Logo */}
      <div className="flex items-center">
        <Link to="/">
          <img
            src="/ISEZERANO LOGO copy - rm bg.png"
            alt="Logo"
            className="w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16"
          />
        </Link>
      </div>

      {/* Hamburger Menu Button (Visible on Mobile) */}
      <button
        className="text-custom-white sm:hidden focus:outline-none z-20 relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div
          className={`w-8 h-1 bg-custom-white rounded transition-transform duration-300 ease-in-out ${isOpen ? "rotate-45 translate-y-2" : ""
            }`}
        ></div>
        <div
          className={`w-8 h-1 bg-custom-white rounded mt-1 transition-opacity duration-300 ease-in-out ${isOpen ? "opacity-0" : ""
            }`}
        ></div>
        <div
          className={`w-8 h-1 bg-custom-white rounded mt-1 transition-transform duration-300 ease-in-out ${isOpen ? "-rotate-45 -translate-y-2" : ""
            }`}
        ></div>
      </button>

      {/* Navigation Links */}
      <ul
        className={`${isOpen ? "translate-x-0" : "translate-x-full"
          } sm:translate-x-0 fixed sm:static top-0 right-0 h-full sm:h-auto w-3/4 sm:w-auto bg-custom-black sm:bg-transparent flex flex-col sm:flex-row gap-6 text-custom-white items-center p-6 sm:p-0 transition-transform duration-300 ease-in-out z-10`}
      >
        {/* About us */}
        {/* <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/about" onClick={() => setIsOpen(false)}>
            Abo turi bo
          </Link>
        </li> */}

        <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/articles" onClick={() => setIsOpen(false)}>
            Inyandiko ngufi
          </Link>
        </li>
        <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/sermons" onClick={() => setIsOpen(false)}>
            Ibyigisho
          </Link>
        </li>
        <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/quotes" onClick={() => setIsOpen(false)}>
            Amagambo yavuzwe
          </Link>
        </li>
        <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/about" onClick={() => setIsOpen(false)}>
            Ibyerekeye
          </Link>
        </li>
        <li className="hover:text-custom-red text-sm sm:text-base w-full text-center sm:w-auto">
          <Link to="/q&a" onClick={() => setIsOpen(false)}>
            Ibibazo n' ibisubizo
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default NavBar;