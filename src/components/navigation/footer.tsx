import { FaFacebook, FaInstagram, FaTiktok, FaYoutube } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { Link } from "react-router-dom";
import { useState } from "react";
import { API_ENDPOINTS } from "../../config/apiConfig";
import { SOCIAL_LINKS } from "../../config/socialLinks";

export function Footer() {
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState("");

  const handleSubscription = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(""); // Clear any previous messages

    if (!email) {
      setMessage("Please enter a valid email address.");
      return;
    }

    try {
      const response = await fetch(API_ENDPOINTS.SUBSCRIBE, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        setMessage("Thank you for subscribing!");
        setEmail(""); // Clear the input field
      } else {
        const errorData = await response.json();
        setMessage(errorData.message || "Subscription failed. Please try again.");
      }
    } catch {
      setMessage("An error occurred. Please try again later.");
    }
  };

  return (
    <div className="px-4 sm:px-8 md:px-12 lg:px-24 bg-custom-black pt-12 sm:pt-16 md:pt-20 text-custom-white font-open-sans mt-8">
      {/* Flex container for footer content */}
      <div className="flex flex-col sm:flex-row gap-8 sm:gap-16 md:gap-24 lg:gap-32">
        {/* Newsletter Section */}
        <div className="flex flex-col items-center sm:items-start">
          <h1 className="pb-5 font-bold text-2xl sm:text-3xl text-center sm:text-left w-full sm:w-56">
            <span className="text-custom-red">Iyandikishe</span>{" "}
            <span className="text-custom-white">
              kugira ngo ujye ubona byinshi.
            </span>
          </h1>
          <form
            onSubmit={handleSubscription}
            className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto"
          >
            <input
              type="email"
              placeholder="Imeli yawe"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              autoComplete="email"
              className="py-2 px-4 bg-gray-600 rounded-md w-full sm:w-48 md:w-56 lg:w-64"
            />
            <button
              type="submit"
              className="py-2 px-6 sm:px-8 bg-custom-red rounded-md whitespace-nowrap"
            >
              Iyandikishe
            </button>
          </form>
          {message && (
            <p className="mt-2 text-sm sm:text-base text-custom-red">
              {message}
            </p>
          )}
        </div>

        {/* Quick Links Section */}
        <div className="text-center sm:text-left">
          <h1 className="font-bold text-xl sm:text-2xl mb-5">Inzira ngufi</h1>
          <div className="font-light flex flex-col gap-3 sm:gap-5">
            <Link to="/" className="hover:text-custom-red">
              Murugo
            </Link>
            <Link to="/articles" className="hover:text-custom-red">
              Inyandiko ngufi
            </Link>
            <Link to="/sermons" className="hover:text-custom-red">
              Ibyigisho
            </Link>
            <Link to="/about" className="hover:text-custom-red">
              Amagambo yavuzwe
            </Link>
            <Link to="/about" className="hover:text-custom-red">
              Ibyerekeye
            </Link>
            <Link to="/quotes" className="hover:text-custom-red">
              Ibibazo n' ibisubizo
            </Link>
          </div>
        </div>

        {/* Reach Out Section */}
        <div className="text-center sm:text-left">
          <h1 className="font-bold text-xl sm:text-2xl mb-5">Dukurikirane</h1>
          <div className="font-light flex flex-col sm:gap-5 gap-3">
            <div className="flex gap-3 items-center justify-center hover:text-custom-red">
              <FaInstagram className="text-custom-grey h-6 w-6" />
              <a href={SOCIAL_LINKS.INSTAGRAM} target="_blank" rel="noopener noreferrer">
                Instagram
              </a>
            </div>
            <div className="flex gap-6 items-center justify-center hover:text-custom-red">
              <FaXTwitter className="text-custom-grey h-6 w-6" />
              <a href={SOCIAL_LINKS.TWITTER} target="_blank" rel="noopener noreferrer">
                Twitter
              </a>
            </div>
            <div className="flex gap-3 items-center justify-center hover:text-custom-red">
              <FaFacebook className="text-custom-grey h-6 w-6" />
              <a href={SOCIAL_LINKS.FACEBOOK} target="_blank" rel="noopener noreferrer">
                Facebook
              </a>
            </div>
            <div className="flex gap-3 items-center justify-center hover:text-custom-red">
              <FaYoutube className="text-custom-grey h-6 w-6" />
              <a href={SOCIAL_LINKS.YOUTUBE} target="_blank" rel="noopener noreferrer">
                Youtube
              </a>
            </div>
            <div className="flex gap-6 items-center justify-center hover:text-custom-red">
              <FaTiktok className="text-custom-grey h-6 w-6" />
              <a href={SOCIAL_LINKS.TIKTOK} target="_blank" rel="noopener noreferrer">
                TikTok
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Divider and Copyright Section */}
      <hr className="border-red-700 mt-8 sm:mt-14" />
      <div>
        <p className="font-light text-center mt-2 pb-2 text-sm sm:text-base">
          Copyright© Impano Y'Imana 2025. All rights reserved
        </p>
      </div>
    </div>
  );
}
