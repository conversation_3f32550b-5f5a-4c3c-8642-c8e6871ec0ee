// filepath: f:\Kevin\work\trade\dev\workspace\impano-y-Imana-frontend\src\components\ScrollToTop.tsx
import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const ScrollToTop = () => {
    const { pathname } = useLocation();

    useEffect(() => {
        window.scrollTo({
            top: 0,
            behavior: "smooth", // Enable smooth scrolling
        });
    }, [pathname]); // Trigger when the route changes

    return null; // This component does not render anything
};

export default ScrollToTop;