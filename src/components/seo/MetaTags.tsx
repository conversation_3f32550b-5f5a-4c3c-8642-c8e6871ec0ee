import { useEffect } from 'react';
import { getAbsoluteUrl, getShareImageSync } from '../../lib/socialShare';
import { SITE_CONFIG } from '../../config/site';

interface MetaTagsProps {
  title: string;
  description: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  siteName?: string;
  article?: any; // Full article object for better image selection
}

const MetaTags: React.FC<MetaTagsProps> = ({
  title,
  description,
  image,
  url,
  type = 'article',
  author,
  publishedTime,
  modifiedTime,
  siteName = SITE_CONFIG.name,
  article
}) => {
  // Use smart image selection if article is provided, otherwise fallback to image prop
  const absoluteImageUrl = article ? getShareImageSync(article) : (image ? getAbsoluteUrl(image) : getAbsoluteUrl(SITE_CONFIG.defaultImage));

  useEffect(() => {
    // Update document title
    document.title = `${title} | ${siteName}`;

    // Helper function to update or create meta tags
    const updateMetaTag = (property: string, content: string, isProperty = true) => {
      const attribute = isProperty ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${property}"]`);

      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, property);
        document.head.appendChild(meta);
      }

      meta.setAttribute('content', content);
    };



    // Basic meta tags with comprehensive Kinyarwanda biblical keywords
    updateMetaTag('description', description, false);
    const kinyarwandaKeywords = [
      // Core salvation terms
      'agakiza', 'gukiranuka', 'icyaha', 'Yesu', 'Kristo', 'ingurane', 'ubutungane',
      'kurimbuka', 'umukiranutsi', 'umunyabyaha', 'amaraso',
      // Additional biblical terms
      'ubwoba bw\'Imana', 'kwicuza', 'kwizera', 'ubugingo', 'ubwiyunge', 'guhinduka',
      'ubwoba', 'ubwenge', 'ubushobozi', 'ubwami bw\'Imana', 'ubunyangamugayo',
      'kwihangana', 'urukundo', 'amahoro', 'ubushake bw\'Imana', 'gusaba', 'gusengera',
      'guhindura', 'ubugingo buhoraho', 'urupfu', 'ubwoba bw\'Uwiteka',
      'ubwiyunge', 'gufata icyemezo', 'kwanga icyaha',
      // Ministry and general terms
      'Impano y\'Imana', 'Ministry', 'Articles', 'Sermons', 'Christian', 'Rwanda', 'Kinyarwanda',
      'inyandiko', 'ibyigisho', 'Imana', 'Uwiteka', 'Bibiliya'
    ];
    updateMetaTag('keywords', kinyarwandaKeywords.join(', '), false);

    // Open Graph meta tags - Essential for Facebook, LinkedIn, WhatsApp
    updateMetaTag('og:title', title);
    updateMetaTag('og:description', description);
    updateMetaTag('og:type', type);
    updateMetaTag('og:site_name', siteName);
    updateMetaTag('og:locale', 'rw_RW'); // Rwanda locale

    if (url) {
      updateMetaTag('og:url', url);
    }

    // Always set image (use default if none provided)
    updateMetaTag('og:image', absoluteImageUrl);
    updateMetaTag('og:image:width', '1200');
    updateMetaTag('og:image:height', '630');
    updateMetaTag('og:image:alt', title);

    // Set image type based on URL
    if (absoluteImageUrl.includes('.jpg') || absoluteImageUrl.includes('.jpeg')) {
      updateMetaTag('og:image:type', 'image/jpeg');
    } else if (absoluteImageUrl.includes('.png') || absoluteImageUrl.startsWith('data:image/png')) {
      updateMetaTag('og:image:type', 'image/png');
    } else {
      updateMetaTag('og:image:type', 'image/png'); // Default to PNG
    }

    // X (Twitter) Card meta tags - Essential for Twitter/X sharing
    updateMetaTag('twitter:card', 'summary_large_image', false);
    updateMetaTag('twitter:title', title, false);
    updateMetaTag('twitter:description', description, false);
    updateMetaTag('twitter:site', '@impanoyimana1', false);
    updateMetaTag('twitter:creator', '@impanoyimana1', false);

    // Always set Twitter image
    updateMetaTag('twitter:image', absoluteImageUrl, false);
    updateMetaTag('twitter:image:alt', title, false);

    // Additional Twitter meta tags for better sharing
    if (url) {
      updateMetaTag('twitter:url', url, false);
    }

    // Article specific meta tags
    if (type === 'article') {
      if (author) {
        updateMetaTag('article:author', author);
      }
      if (publishedTime) {
        updateMetaTag('article:published_time', publishedTime);
      }
      if (modifiedTime) {
        updateMetaTag('article:modified_time', modifiedTime);
      }
      updateMetaTag('article:section', 'Articles');
    }

    // Additional SEO meta tags
    updateMetaTag('robots', 'index, follow', false);
    updateMetaTag('language', 'rw', false);
    updateMetaTag('author', author || siteName, false);

    // Canonical URL for SEO
    if (url) {
      let canonicalLink = document.querySelector('link[rel="canonical"]');
      if (!canonicalLink) {
        canonicalLink = document.createElement('link');
        canonicalLink.setAttribute('rel', 'canonical');
        document.head.appendChild(canonicalLink);
      }
      canonicalLink.setAttribute('href', url);
    }

    // Theme color for mobile browsers
    updateMetaTag('theme-color', SITE_CONFIG.themeColor, false);

    // Additional meta tags for better social sharing
    updateMetaTag('application-name', siteName, false);
    updateMetaTag('msapplication-TileColor', SITE_CONFIG.themeColor, false);



    // Cleanup function to remove meta tags when component unmounts
    return () => {
      // We don't remove meta tags on unmount as they should persist
      // for the current page until new ones are set
    };
  }, [title, description, image, url, type, author, publishedTime, modifiedTime, siteName, absoluteImageUrl]);

  return null; // This component doesn't render anything
};

export default MetaTags;
