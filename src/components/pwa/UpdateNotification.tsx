import React, { useState } from 'react';
import { FaDownload, FaTimes, FaSync } from 'react-icons/fa';

interface UpdateNotificationProps {
  onUpdate: () => void;
  onDismiss: () => void;
}

const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  onUpdate,
  onDismiss
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await onUpdate();
      // Don't reset isUpdating here as the page will reload
    } catch (error) {
      console.error('Update failed:', error);
      setIsUpdating(false);
      // The error handling is now done in the hook
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(onDismiss, 300);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 left-4 right-4 z-50 animate-slide-down">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4 mx-auto max-w-md">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <FaSync className="text-blue-600 text-lg" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-gray-900 mb-1">
              Impano y'Imana - Ivugurura rishya
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              Hari amakuru mashya. Kanda "Vugurura" kugira ngo ubone ibintu bishya.
            </p>
            
            <div className="flex space-x-2">
              <button
                onClick={handleUpdate}
                disabled={isUpdating}
                className="flex items-center space-x-2 bg-custom-red text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUpdating ? (
                  <>
                    <FaSync className="text-xs animate-spin" />
                    <span>Biravugururwa...</span>
                  </>
                ) : (
                  <>
                    <FaDownload className="text-xs" />
                    <span>Vugurura</span>
                  </>
                )}
              </button>
              
              <button
                onClick={handleDismiss}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                Nyuma
              </button>
            </div>
          </div>
          
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 text-gray-400 hover:text-gray-600"
          >
            <FaTimes className="text-sm" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default UpdateNotification;