import React from 'react';
import { FaApple, FaAndroid, FaChrome, FaShare, FaPlus } from 'react-icons/fa';

const PWAInstructions: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
        Shyira Impano y'Imana kuri Telefoni Yawe
      </h2>
      
      <div className="space-y-6">
        {/* iOS Instructions */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <FaApple className="text-gray-600 text-xl mr-2" />
            <h3 className="text-lg font-semibold">iPhone/iPad (Safari)</h3>
          </div>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Fungura Safari browser</li>
            <li><PERSON>ya kuri impanoyimana.org</li>
            <li>Kanda button ya <FaShare className="inline text-blue-500" /> (Share) hepfo</li>
            <li>Hitamo "Add to Home Screen"</li>
            <li>Kanda "Add" kugira ngo urangize</li>
          </ol>
        </div>

        {/* Android Chrome Instructions */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <FaAndroid className="text-green-600 text-xl mr-2" />
            <h3 className="text-lg font-semibold">Android (Chrome)</h3>
          </div>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Fungura Chrome browser</li>
            <li>Jya kuri impanoyimana.org</li>
            <li>Reba message ya "Install App" hejuru</li>
            <li>Kanda "Install" cyangwa "Add to Home Screen"</li>
            <li>Emeza installation</li>
          </ol>
          <p className="text-xs text-gray-500 mt-2">
            Cyangwa: Menu (3 dots) → "Add to Home screen"
          </p>
        </div>

        {/* Desktop Chrome Instructions */}
        <div className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <FaChrome className="text-blue-500 text-xl mr-2" />
            <h3 className="text-lg font-semibold">Desktop (Chrome/Edge)</h3>
          </div>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Fungura Chrome cyangwa Edge</li>
            <li>Jya kuri impanoyimana.org</li>
            <li>Reba install icon <FaPlus className="inline text-green-500" /> mu address bar</li>
            <li>Kanda icon kugira ngo ushyire app</li>
            <li>Kanda "Install" kugira ngo urangize</li>
          </ol>
        </div>
      </div>

      <div className="mt-6 p-4 bg-custom-grey-2 rounded-lg">
        <h4 className="font-semibold text-gray-900 mb-2">Inyungu z'App:</h4>
        <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
          <li>Gufungura vuba nta kureba browser</li>
          <li>Gusoma offline (iyo nta internet)</li>
          <li>Notifications z'inyandiko nshya</li>
          <li>Experience nziza ya mobile</li>
        </ul>
      </div>
    </div>
  );
};

export default PWAInstructions;
