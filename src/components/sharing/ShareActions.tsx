import {
  Fa<PERSON><PERSON>ck,
  FaCopy,
  FaEnvelope,
  FaFacebook,
  FaLinkedin,
  FaShare,
  FaTelegram,
  FaWhatsapp,
} from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { useState } from "react";

interface ShareActionsProps {
  url: string;
  message?: string; // e.g., "\"Quote text\" — Author"
  showNativeShare?: boolean;
  onShared?: () => void;
}

function canUseNativeShare() {
  return typeof navigator !== "undefined" && "share" in navigator;
}

export default function ShareActions({
  url,
  message,
  showNativeShare = true,
  onShared,
}: ShareActionsProps) {
  const [copied, setCopied] = useState(false);

  const openPopup = (shareUrl: string, width = 600, height = 400) => {
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;
    window.open(
      shareUrl,
      "_blank",
      `width=${width},height=${height},left=${left},top=${top}`
    );
  };

  const handleShare = async (
    platform:
      | "native"
      | "facebook"
      | "x"
      | "linkedin"
      | "whatsapp"
      | "telegram"
      | "email"
  ) => {
    try {
      switch (platform) {
        case "native":
          if (canUseNativeShare()) {
            await (navigator as any).share({
              title: document.title,
              text: message,
              url,
            });
          }
          break;
        case "facebook": {
          const share = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
            url
          )}${message ? `&quote=${encodeURIComponent(message)}` : ""}`;
          openPopup(share);
          break;
        }
        case "x": {
          const share = `https://twitter.com/intent/tweet?text=${encodeURIComponent(
            message || ""
          )}&url=${encodeURIComponent(url)}`;
          openPopup(share);
          break;
        }
        case "linkedin": {
          const share = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
            url
          )}`;
          openPopup(share);
          break;
        }
        case "whatsapp": {
          const share = `https://wa.me/?text=${encodeURIComponent(
            `${message ? message + "\n\n" : ""}${url}`
          )}`;
          window.open(share, "_blank");
          break;
        }
        case "telegram": {
          const share = `https://t.me/share/url?url=${encodeURIComponent(
            url
          )}&text=${encodeURIComponent(message || "")}`;
          window.open(share, "_blank");
          break;
        }
        case "email": {
          const subject = encodeURIComponent(document.title);
          const body = encodeURIComponent(
            `${message ? message + "\n\n" : ""}${url}`
          );
          window.location.href = `mailto:?subject=${subject}&body=${body}`;
          break;
        }
      }
      onShared && onShared();
    } catch (e) {
      // no-op; user cancelled native share
    }
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
      onShared && onShared();
    } catch (error) {
      console.error("Failed to copy link:", error);
    }
  };

  return (
    <div className="p-6 space-y-3 max-h-[60vh] overflow-y-auto">
      {showNativeShare && canUseNativeShare() && (
        <button
          onClick={() => handleShare("native")}
          className="flex items-center gap-4 w-full p-4 text-left hover:bg-custom-red/5 rounded-xl transition-colors group"
        >
          <div className="w-12 h-12 bg-custom-red/10 rounded-full flex items-center justify-center group-hover:bg-custom-red/20 transition-colors">
            <FaShare className="text-custom-red text-lg" />
          </div>
          <div>
            <p className="font-medium text-gray-900">Sangiza</p>
            <p className="text-sm text-gray-500">
              Hitamo uburyo bwiza bwo gusangira
            </p>
          </div>
        </button>
      )}

      <button
        onClick={() => handleShare("facebook")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-blue-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
          <FaFacebook className="text-blue-600 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">Facebook</p>
          <p className="text-sm text-gray-500">Sangiza kuri Facebook</p>
        </div>
      </button>

      <button
        onClick={() => handleShare("x")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
          <FaXTwitter className="text-gray-700 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">X</p>
          <p className="text-sm text-gray-500">Sangiza kuri X (Twitter)</p>
        </div>
      </button>

      <button
        onClick={() => handleShare("linkedin")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-blue-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
          <FaLinkedin className="text-blue-700 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">LinkedIn</p>
          <p className="text-sm text-gray-500">Sangiza kuri LinkedIn</p>
        </div>
      </button>

      <button
        onClick={() => handleShare("whatsapp")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-green-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
          <FaWhatsapp className="text-green-600 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">WhatsApp</p>
          <p className="text-sm text-gray-500">Sangiza kuri WhatsApp</p>
        </div>
      </button>

      <button
        onClick={() => handleShare("telegram")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
          <FaTelegram className="text-blue-500 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">Telegram</p>
          <p className="text-sm text-gray-500">Sangiza kuri Telegram</p>
        </div>
      </button>

      <button
        onClick={() => handleShare("email")}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
          <FaEnvelope className="text-gray-600 text-lg" />
        </div>
        <div>
          <p className="font-medium text-gray-900">Email</p>
          <p className="text-sm text-gray-500">Sangiza muri Email</p>
        </div>
      </button>

      <button
        onClick={handleCopyToClipboard}
        className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
      >
        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
          {copied ? (
            <FaCheck className="text-green-600 text-lg" />
          ) : (
            <FaCopy className="text-gray-600 text-lg" />
          )}
        </div>
        <div>
          <p className="font-medium text-gray-900">
            {copied ? "Byarakopishijwe!" : "Kopisha link"}
          </p>
          <p className="text-sm text-gray-500">
            {copied
              ? "Link yarakopishijwe mu clipboard"
              : "Kopisha link y'inyandiko"}
          </p>
        </div>
      </button>
    </div>
  );
}
