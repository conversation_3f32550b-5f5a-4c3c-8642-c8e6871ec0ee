import React, { useState, useEffect } from 'react';
import { FaShare, FaTimes } from 'react-icons/fa';
// sharing URLs handled inside ShareActions
import ShareActions from './ShareActions';


interface SocialShareProps {
  url: string;
  title: string;
  description: string;
  className?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description,
  className = ""
}) => {
  const [showModal, setShowModal] = useState(false);

  // kept for potential future use; current sharing handled by ShareActions

  // all share actions are handled by the shared component now

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowModal(false);
      }
    };

    if (showModal) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [showModal]);

  return (
    <>
      {/* Share Button */}
      <button
        onClick={() => setShowModal(true)}
        className={`flex items-center gap-2 px-4 py-2 bg-custom-red text-white rounded-lg hover:bg-red-700 transition-colors duration-200 ${className}`}
      >
        <FaShare className="text-sm" />
        <span className="text-sm font-medium">Sangiza</span>
      </button>

      {/* Modal Overlay */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end sm:items-center justify-center p-4 overflow-y-auto">
          {/* Modal Content */}
          <div className="bg-white rounded-t-2xl sm:rounded-2xl w-full max-w-md max-h-[90vh] sm:max-h-[80vh] overflow-y-auto animate-slide-up sm:animate-fade-in">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Sangiza inyandiko</h3>
              <button
                onClick={() => setShowModal(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <FaTimes className="text-gray-500" />
              </button>
            </div>

            {/* Share Options */}
            <ShareActions
              url={url}
              message={description || title}
              showNativeShare={true}
              onShared={() => setShowModal(false)}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default SocialShare;
