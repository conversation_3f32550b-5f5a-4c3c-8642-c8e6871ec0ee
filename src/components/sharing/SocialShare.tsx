import React, { useState, useEffect } from 'react';
import {
  FaFacebook,
  FaLinkedin,
  FaWhatsapp,
  FaCopy,
  FaShare,
  FaCheck,
  FaTimes,
  FaEnvelope,
  FaTelegram
} from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { socialShareUrls, copyToClipboard, nativeShare, canUseNativeShare, openShareWindow } from '../../lib/socialShare';


interface SocialShareProps {
  url: string;
  title: string;
  description: string;
  className?: string;
}

const SocialShare: React.FC<SocialShareProps> = ({
  url,
  title,
  description,
  className = ""
}) => {
  const [copied, setCopied] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const shareLinks = {
    facebook: socialShareUrls.facebook(url),
    x: socialShareUrls.twitter(url, title, ['ImpanoyImana', 'Ministry']),
    linkedin: socialShareUrls.linkedin(url),
    whatsapp: socialShareUrls.whatsapp(url, title),
    telegram: socialShareUrls.telegram(url, title),
    email: socialShareUrls.email(url, title, description),
  };

  // Handle native sharing if available
  const handleNativeShare = async () => {
    const success = await nativeShare({
      title: title,
      text: description,
      url: url,
    });
    if (success) {
      setShowModal(false);
    }
    return success;
  };

  const handleCopyToClipboard = async () => {
    const success = await copyToClipboard(url);
    if (success) {
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
        setShowModal(false);
      }, 1500);
    }
  };

  const handleShare = async (platform: string) => {
    if (platform === 'native') {
      const shared = await handleNativeShare();
      if (shared) return;
    }

    // Fallback to platform-specific sharing
    const shareUrl = shareLinks[platform as keyof typeof shareLinks];
    if (shareUrl) {
      // Use popup window for better UX on desktop
      if (platform === 'facebook' || platform === 'x' || platform === 'linkedin') {
        openShareWindow(shareUrl, `Share on ${platform}`, 600, 400);
      } else {
        window.open(shareUrl, '_blank', 'noopener,noreferrer');
      }
      setShowModal(false);
    }
  };

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setShowModal(false);
      }
    };

    if (showModal) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [showModal]);

  return (
    <>
      {/* Share Button */}
      <button
        onClick={() => setShowModal(true)}
        className={`flex items-center gap-2 px-4 py-2 bg-custom-red text-white rounded-lg hover:bg-red-700 transition-colors duration-200 ${className}`}
      >
        <FaShare className="text-sm" />
        <span className="text-sm font-medium">Sangiza</span>
      </button>

      {/* Modal Overlay */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end sm:items-center justify-center p-4 overflow-y-auto">
          {/* Modal Content */}
          <div className="bg-white rounded-t-2xl sm:rounded-2xl w-full max-w-md max-h-[90vh] sm:max-h-[80vh] overflow-y-auto animate-slide-up sm:animate-fade-in">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Sangiza inyandiko</h3>
              <button
                onClick={() => setShowModal(false)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <FaTimes className="text-gray-500" />
              </button>
            </div>

            {/* Share Options */}
            <div className="p-6 space-y-3 max-h-[60vh] overflow-y-auto">
              {/* Native Share (if available) */}
              {canUseNativeShare() && (
                <button
                  onClick={() => handleShare('native')}
                  className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
                >
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                    <FaShare className="text-blue-600 text-lg" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Sangiza</p>
                    <p className="text-sm text-gray-500">Hitamo uburyo bwiza bwo gusangira</p>
                  </div>
                </button>
              )}

              {/* Facebook */}
              <button
                onClick={() => handleShare('facebook')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <FaFacebook className="text-blue-600 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Facebook</p>
                  <p className="text-sm text-gray-500">Sangiza kuri Facebook</p>
                </div>
              </button>

              {/* X (Twitter) */}
              <button
                onClick={() => handleShare('x')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                  <FaXTwitter className="text-gray-700 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">X</p>
                  <p className="text-sm text-gray-500">Sangiza kuri X (Twitter)</p>
                </div>
              </button>

              {/* LinkedIn */}
              <button
                onClick={() => handleShare('linkedin')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <FaLinkedin className="text-blue-700 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">LinkedIn</p>
                  <p className="text-sm text-gray-500">Sangiza kuri LinkedIn</p>
                </div>
              </button>

              {/* WhatsApp */}
              <button
                onClick={() => handleShare('whatsapp')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                  <FaWhatsapp className="text-green-600 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">WhatsApp</p>
                  <p className="text-sm text-gray-500">Sangiza kuri WhatsApp</p>
                </div>
              </button>

              {/* Telegram */}
              <button
                onClick={() => handleShare('telegram')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <FaTelegram className="text-blue-500 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Telegram</p>
                  <p className="text-sm text-gray-500">Sangiza kuri Telegram</p>
                </div>
              </button>

              {/* Email */}
              <button
                onClick={() => handleShare('email')}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                  <FaEnvelope className="text-gray-600 text-lg" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Email</p>
                  <p className="text-sm text-gray-500">Sangiza muri Email</p>
                </div>
              </button>

              {/* Copy Link */}
              <button
                onClick={handleCopyToClipboard}
                className="flex items-center gap-4 w-full p-4 text-left hover:bg-gray-50 rounded-xl transition-colors group"
              >
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center group-hover:bg-gray-200 transition-colors">
                  {copied ? (
                    <FaCheck className="text-green-600 text-lg" />
                  ) : (
                    <FaCopy className="text-gray-600 text-lg" />
                  )}
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {copied ? 'Byarakopishijwe!' : 'Kopisha link'}
                  </p>
                  <p className="text-sm text-gray-500">
                    {copied ? 'Link yarakopishijwe mu clipboard' : 'Kopisha link y\'inyandiko'}
                  </p>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SocialShare;
