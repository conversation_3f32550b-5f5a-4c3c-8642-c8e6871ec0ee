import { Quote } from "../../interfaces/Quote";
import { FaShare } from "react-icons/fa";
import { useState } from "react";
import ShareActions from "../sharing/ShareActions";

interface QuoteCardProps {
  quote: Quote;
  variant?: "grid" | "detail";
  onQuoteClick?: (quote: Quote) => void;
}

export default function QuoteCard({
  quote,
  variant = "grid",
  onQuoteClick,
}: QuoteCardProps) {
  const [showShare, setShowShare] = useState(false);

  const handleShare = () => {
    setShowShare(true);
  };

  const cardId = `quote-card-${quote.id}`;
  const currentUrl = `${window.location.origin}/quotes/${quote.id}`;

  // Truncate text for grid view
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength).trim() + "...";
  };

  if (variant === "detail") {
    return (
      <div className="relative mx-auto w-full">
        <div
          id={cardId}
          className="bg-gradient-to-br from-custom-red/5 to-custom-red/10 rounded-2xl p-6 sm:p-8 lg:p-12 shadow-lg border border-custom-red/20 backdrop-blur-sm"
        >
          {/* Quote Icon */}
          <div className="flex justify-center mb-6 lg:mb-8">
            <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 bg-custom-red/10 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-custom-red/20">
              <svg
                className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 text-custom-red"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
              </svg>
            </div>
          </div>

          {/* Quote Text */}
          <blockquote className="text-center mb-6 lg:mb-8 px-4">
            <p className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-medium text-gray-800 leading-relaxed italic w-full mx-auto">
              "{quote.text}"
            </p>
          </blockquote>

          {/* Author Info */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6 lg:mb-8">
            <div className="relative group">
              <img
                src={quote.author_image}
                alt={quote.author}
                className="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-full object-cover border-4 border-white shadow-lg transition-transform duration-300 group-hover:scale-105"
              />
              <div className="absolute inset-0 rounded-full bg-custom-red/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="text-center sm:text-left">
              <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold text-gray-800 mb-1">
                {quote.author}
              </h3>
              {quote.categories.length > 0 && (
                <div className="flex flex-wrap justify-center sm:justify-start gap-2">
                  {quote.categories.map((cat, index) => (
                    <span
                      key={index}
                      className="inline-block px-3 py-1 text-xs sm:text-sm text-custom-red font-medium bg-custom-red/10 rounded-full"
                    >
                      {cat.name}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons flex flex-col sm:flex-row justify-center items-center gap-3 sm:gap-4">
            <button
              onClick={handleShare}
              className="w-full sm:w-auto flex items-center justify-center gap-2 px-6 lg:px-8 py-3 lg:py-4 bg-custom-red text-white rounded-xl hover:bg-red-700 focus:outline-none focus:ring-4 focus:ring-custom-red/30 transition-all duration-300 transform hover:scale-105 active:scale-95"
              aria-label="Share quote"
            >
              <FaShare className="w-4 h-4" />
              <span className="font-medium">Sangiza</span>
            </button>

            {/* Logo */}
            <div className="flex-shrink-0">
              <img
                src="/IMPANO Y'IMANA logo.png"
                alt="Impano y'Imana"
                className="w-12 h-12 sm:w-16 sm:h-16 object-contain opacity-60 hover:opacity-80 transition-opacity duration-300"
              />
            </div>
          </div>
        </div>

        {/* Share Modal Overlay */}
        {showShare && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-800">
                  Sangiza ibyavuzwe
                </h3>
                <button
                  onClick={() => setShowShare(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              {/* Direct Social Media Options (shared component) */}
              <ShareActions
                url={currentUrl}
                message={`"${quote.text}" — ${quote.author}`}
                showNativeShare={true}
                onShared={() => setShowShare(false)}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  // Grid variant - Flexible layout
  const hasImage =
    quote.author_image &&
    quote.author_image.trim() !== "" &&
    !quote.author_image.includes("placeholder") &&
    !quote.author_image.includes("default");

  return (
    <div className="group relative h-full overflow-hidden rounded-xl">
      <div
        id={cardId}
        className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden cursor-pointer h-full ${
          hasImage
            ? "flex flex-row"
            : "flex flex-col bg-gradient-to-br from-custom-red/5 to-custom-red/10"
        }`}
        onClick={() => onQuoteClick?.(quote)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            onQuoteClick?.(quote);
          }
        }}
        aria-label={`View quote by ${quote.author}`}
      >
        {/* Author Image - Left side (only if image exists) */}
        {hasImage && (
          <div className="relative w-2/5 min-w-[160px] max-w-[240px] overflow-hidden">
            <img
              src={quote.author_image}
              alt={quote.author}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-transparent" />

            {/* Quote Icon Overlay */}
            <div className="absolute top-2 right-2 w-6 h-6 sm:w-8 sm:h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center transition-all duration-300 group-hover:bg-white/30 group-hover:scale-110">
              <svg
                className="w-3 h-3 sm:w-4 sm:h-4 text-white"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
              </svg>
            </div>
          </div>
        )}

        {/* Quote Content - Right side or full width */}
        <div
          className={`${
            hasImage ? "flex-1" : "w-full"
          } p-3 sm:p-4 flex flex-col justify-between relative`}
        >
          {/* Logo - Top Right */}
          <div className="absolute top-3 right-3 z-10">
            <img
              src="/IMPANO Y'IMANA logo.png"
              alt="Impano y'Imana"
              className="w-10 h-10 sm:w-12 sm:h-12 object-contain opacity-60 hover:opacity-80 transition-opacity duration-300"
            />
          </div>
          {/* Quote Icon for cards without images */}
          {!hasImage && (
            <div className="flex justify-center mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-custom-red/10 to-custom-red/20 rounded-full flex items-center justify-center shadow-sm">
                <svg
                  className="w-6 h-6 text-custom-red"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h3.983v10h-9.983z" />
                </svg>
              </div>
            </div>
          )}

          {/* Quote Text and Author */}
          <div className="flex-grow pr-16">
            <blockquote className="mb-2">
              <p className="text-gray-700 leading-relaxed text-sm sm:text-base line-clamp-3">
                "{truncateText(quote.text, 100)}"
              </p>
            </blockquote>

            {/* Author name */}
            <h3 className="font-semibold text-gray-800 text-sm sm:text-base mb-2">
              {quote.author}
            </h3>

            {/* Categories */}
            {quote.categories.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {quote.categories.slice(0, 2).map((cat, index) => (
                  <span
                    key={index}
                    className="inline-block px-2 py-1 text-xs text-custom-red font-medium bg-custom-red/10 rounded-full"
                  >
                    {cat.name}
                  </span>
                ))}
                {quote.categories.length > 2 && (
                  <span className="inline-block px-2 py-1 text-xs text-gray-500 font-medium bg-gray-100 rounded-full">
                    +{quote.categories.length - 2}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="action-buttons flex justify-start items-center">
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleShare();
              }}
              className="flex items-center gap-1 px-3 py-2 text-xs bg-custom-red text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-gray-300 transition-all duration-300 transform hover:scale-105 active:scale-95"
              aria-label="Share quote"
            >
              <FaShare className="w-3 h-3" />
              <span className="font-medium hidden sm:inline">Sangiza</span>
            </button>
          </div>
        </div>
      </div>

      {/* Share Modal Overlay */}
      {showShare && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-800">
                Sangiza ibyavuzwe
              </h3>
              <button
                onClick={() => setShowShare(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg
                  className="w-6 h-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Direct Social Media Options (shared component) */}
            <ShareActions
              url={currentUrl}
              message={`"${quote.text}" — ${quote.author}`}
              showNativeShare={true}
              onShared={() => setShowShare(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
