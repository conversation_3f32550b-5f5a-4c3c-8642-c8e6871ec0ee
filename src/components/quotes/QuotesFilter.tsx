import { useState } from 'react';
import { FaSearch, FaFilter, FaTimes } from 'react-icons/fa';

interface QuotesFilterProps {
    onSearch: (query: string) => void;
    onFilterByAuthor: (author: string) => void;
    onFilterByCategory: (category: string) => void;
    onClearFilters: () => void;
    authors: string[];
    categories: string[];
    loading?: boolean;
}

export default function QuotesFilter({
    onSearch,
    onFilterByAuthor,
    onFilterByCategory,
    onClearFilters,
    authors,
    categories,
    loading = false
}: QuotesFilterProps) {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedAuthor, setSelectedAuthor] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [showFilters, setShowFilters] = useState(false);

    const handleSearch = (query: string) => {
        setSearchQuery(query);
        onSearch(query);
    };

    const handleAuthorFilter = (author: string) => {
        setSelectedAuthor(author);
        onFilterByAuthor(author);
    };

    const handleCategoryFilter = (category: string) => {
        setSelectedCategory(category);
        onFilterByCategory(category);
    };

    const handleClearFilters = () => {
        setSearchQuery('');
        setSelectedAuthor('');
        setSelectedCategory('');
        onClearFilters();
    };

    const hasActiveFilters = searchQuery || selectedAuthor || selectedCategory;

    return (
        <div className="w-full">
            {/* Main Search and Filter Row */}
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center w-full">
                {/* Search Bar */}
                <div className="relative flex-1 min-w-0 w-full">
                    <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                        type="text"
                        placeholder="Shakisha amagambo yavuzwe..."
                        value={searchQuery}
                        onChange={(e) => handleSearch(e.target.value)}
                        className="w-full pl-10 pr-4 py-2.5 rounded-lg focus:outline-none focus:ring-2 focus:ring-custom-red focus:border-custom-red transition-all text-base shadow-sm"
                        disabled={loading}
                    />
                </div>

                {/* Filter Controls */}
                <div className="flex items-center gap-3">
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className={`flex items-center gap-2 px-4 py-2.5 rounded-lg border-2 transition-all ${
                            showFilters 
                                ? 'border-custom-red bg-custom-red text-white shadow-lg' 
                                : 'border-gray-200 bg-white text-gray-600 hover:border-custom-red hover:text-custom-red'
                        }`}
                    >
                        <FaFilter className="w-4 h-4" />
                        <span className="font-medium">Filter</span>
                    </button>

                    {hasActiveFilters && (
                        <button
                            onClick={handleClearFilters}
                            className="flex items-center gap-2 px-4 py-2.5 bg-red-50 border-2 border-red-200 text-red-600 rounded-lg hover:bg-red-100 hover:border-red-300 transition-all font-medium"
                        >
                            <FaTimes className="w-4 h-4" />
                            <span>Clear</span>
                        </button>
                    )}
                </div>
            </div>

            {/* Filter Options */}
            {showFilters && (
                <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Author Filter */}
                        <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                                Author
                            </label>
                            <select
                                value={selectedAuthor}
                                onChange={(e) => handleAuthorFilter(e.target.value)}
                                className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-custom-red focus:border-custom-red transition-all text-base"
                                disabled={loading}
                            >
                                <option value="">All Authors</option>
                                {authors.map((author) => (
                                    <option key={author} value={author}>
                                        {author}
                                    </option>
                                ))}
                            </select>
                        </div>

                        {/* Category Filter */}
                        <div>
                            <label className="block text-sm font-semibold text-gray-700 mb-2">
                                Category
                            </label>
                            <select
                                value={selectedCategory}
                                onChange={(e) => handleCategoryFilter(e.target.value)}
                                className="w-full px-3 py-2 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-custom-red focus:border-custom-red transition-all text-base"
                                disabled={loading}
                            >
                                <option value="">All Categories</option>
                                {categories.map((category) => (
                                    <option key={category} value={category}>
                                        {category}
                                    </option>
                                ))}
                            </select>
                        </div>
                    </div>
                </div>
            )}

            {/* Active Filters Display */}
            {hasActiveFilters && (
                <div className="mt-4 flex flex-wrap gap-3">
                    {searchQuery && (
                        <span className="inline-flex items-center gap-2 px-4 py-2 bg-custom-red/10 text-custom-red rounded-full text-sm font-medium border border-custom-red/20">
                            <FaSearch className="w-3 h-3" />
                            "{searchQuery}"
                            <button
                                onClick={() => handleSearch('')}
                                className="ml-1 hover:text-red-700 transition-colors"
                            >
                                <FaTimes className="w-3 h-3" />
                            </button>
                        </span>
                    )}
                    {selectedAuthor && (
                        <span className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-full text-sm font-medium border border-blue-200">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            {selectedAuthor}
                            <button
                                onClick={() => handleAuthorFilter('')}
                                className="ml-1 hover:text-blue-900 transition-colors"
                            >
                                <FaTimes className="w-3 h-3" />
                            </button>
                        </span>
                    )}
                    {selectedCategory && (
                        <span className="inline-flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-full text-sm font-medium border border-green-200">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            {selectedCategory}
                            <button
                                onClick={() => handleCategoryFilter('')}
                                className="ml-1 hover:text-green-900 transition-colors"
                            >
                                <FaTimes className="w-3 h-3" />
                            </button>
                        </span>
                    )}
                </div>
            )}
        </div>
    );
}
