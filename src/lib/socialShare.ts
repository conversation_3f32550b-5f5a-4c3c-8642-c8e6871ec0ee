import { SITE_CONFIG, getDefaultHashtags } from '../config/site';

// Utility function to strip HTML tags
export const stripHtml = (html: string, maxLength?: number): string => {
  if (!html) return '';
  
  const text = html.replace(/<[^>]*>/g, '').trim();
  return maxLength ? text.substring(0, maxLength) + (text.length > maxLength ? '...' : '') : text;
};

// Ensure absolute URL
export const getAbsoluteUrl = (url: string): string => {
  if (!url) return `${SITE_CONFIG.url}${SITE_CONFIG.defaultImage}`;
  if (url.startsWith('http')) return url;
  if (url.startsWith('//')) return `https:${url}`;
  return `${SITE_CONFIG.url}${url.startsWith('/') ? url : `/${url}`}`;
};

// Validate if an image URL is accessible with timeout and better error handling
export const validateImageUrl = async (url: string, timeoutMs: number = 5000): Promise<boolean> => {
  if (!url || url.trim() === '') return false;

  // Skip validation for data URLs (they're always valid if properly formatted)
  if (url.startsWith('data:image/')) return true;

  try {
    // Create a timeout promise
    const timeoutPromise = new Promise<Response>((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), timeoutMs);
    });

    // Race between fetch and timeout
    const response = await Promise.race([
      fetch(url, {
        method: 'HEAD',
        mode: 'no-cors', // Allow cross-origin requests
        cache: 'no-cache'
      }),
      timeoutPromise
    ]);

    const contentType = response.headers.get('content-type');
    return response.ok && (contentType?.startsWith('image/') || false);
  } catch (error) {
    return false;
  }
};

// Preload image to ensure it's accessible
export const preloadImage = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    if (!url || url.trim() === '') {
      resolve(false);
      return;
    }

    // Skip preloading for data URLs
    if (url.startsWith('data:image/')) {
      resolve(true);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;

    // Timeout after 10 seconds
    setTimeout(() => resolve(false), 10000);
  });
};



// Get the best image for sharing with improved validation and fallbacks
export const getShareImage = async (item: any): Promise<string> => {
  // Priority: featured_image > image > default image
  if (item?.featured_image && item.featured_image.trim() !== '') {
    return item.featured_image; // Return the full URL directly from API
  }

  if (item?.image && item.image.trim() !== '') {
    return item.image; // Return the image URL directly from API
  }

  // Fallback to black background image for social sharing
  return getAbsoluteUrl('/social-share-fallback.svg');
};

// Synchronous version for backward compatibility
export const getShareImageSync = (item: any): string => {
  // Priority: featured_image > image > default image with black background
  if (item?.featured_image && item.featured_image.trim() !== '') {
    return item.featured_image; // Return the full URL directly from API
  }

  if (item?.image && item.image.trim() !== '') {
    return item.image; // Return the image URL directly from API
  }

  // Fallback to black background image for social sharing
  return getAbsoluteUrl('/social-share-fallback.svg');
};

// Generate sharing data for articles
export const generateShareData = (item: any, type: 'article' | 'sermon' = 'article', baseUrl?: string) => {
  const currentUrl = baseUrl || window.location.href;
  const title = item.title || 'Impano y\'Imana Ministry';
  const description = stripHtml(item.short_description || item.content || SITE_CONFIG.description, 160);
  const hashtags = getDefaultHashtags(type);

  const image = getShareImageSync(item);

  return {
    url: currentUrl,
    title,
    description,
    hashtags,
    image
  };
};

// Social sharing URLs
export const socialShareUrls = {
  facebook: (url: string) => {
    return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
  },
  
  twitter: (url: string, title: string, hashtags?: string[]) => {
    const text = title;
    const hashtagString = hashtags ? hashtags.map(tag => `#${tag}`).join(' ') : '';
    const fullText = `${text} ${hashtagString}`.trim();
    return `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(fullText)}`;
  },
  
  linkedin: (url: string) => {
    return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
  },
  
  whatsapp: (url: string, title: string) => {
    const text = `${title}\n${url}`;
    return `https://wa.me/?text=${encodeURIComponent(text)}`;
  },
  
  telegram: (url: string, title: string) => {
    return `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
  },
  
  email: (url: string, title: string, body?: string) => {
    const subject = `Reba inyandiko: ${title}`;
    const emailBody = body || `Reba inyandiko nziza: ${title}\n\n${url}`;
    return `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(emailBody)}`;
  },
  
  instagram: (url: string, title: string, hashtags?: string[]) => {
    const hashtagString = hashtags ? hashtags.map(tag => `#${tag}`).join(' ') : '';
    return `${title}\n\n${hashtagString}\n\nLink mu bio: ${url}`;
  }
};

// Copy to clipboard with fallback
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      textArea.remove();
      return result;
    }
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

// Open share window with optimal dimensions
export const openShareWindow = (url: string, title: string, width = 600, height = 400) => {
  const left = (window.screen.width - width) / 2;
  const top = (window.screen.height - height) / 2;
  
  window.open(
    url,
    title,
    `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`
  );
};

// Native sharing support
export const canUseNativeShare = (): boolean => {
  return typeof navigator !== 'undefined' && 'share' in navigator;
};

export const nativeShare = async (data: { title: string; text: string; url: string }): Promise<boolean> => {
  if (!canUseNativeShare()) return false;

  try {
    await navigator.share(data);
    return true;
  } catch (error) {
    console.log('Native sharing cancelled or failed:', error);
    return false;
  }
};

// Debug function to test social sharing URLs
export const testSocialSharing = (article: any) => {
  console.log('🧪 Social Sharing Debug Test');
  console.log('📄 Article data:', {
    title: article.title,
    featured_image: article.featured_image,
    short_description: article.short_description?.substring(0, 100) + '...'
  });

  const shareData = generateShareData(article, 'article');
  console.log('🔗 Generated share data:', shareData);

  const testUrls = {
    facebook: socialShareUrls.facebook(shareData.url),
    twitter: socialShareUrls.twitter(shareData.url, shareData.title, shareData.hashtags),
    linkedin: socialShareUrls.linkedin(shareData.url),
    whatsapp: socialShareUrls.whatsapp(shareData.url, shareData.title)
  };

  console.log('🌐 Social sharing URLs:', testUrls);

  return {
    shareData,
    urls: testUrls
  };
};
