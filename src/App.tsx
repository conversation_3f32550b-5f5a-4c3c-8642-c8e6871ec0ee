import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Analytics } from "@vercel/analytics/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import HomePage from "./pages/HomePage";
import ComingSoon from "./pages/ComingSoon";
import ArticleDetailPage from "./pages/Articles/ArticleDetailPage";
import ArticlesListPage from "./pages/Articles/ArticlesListPage";
import NotFoundPage from "./pages/NotFoundPage";
import ScrollToTop from "./components/navigation/ScrollToTop";
import InstallPrompt from "./components/pwa/InstallPrompt";
import { usePWAUpdate } from "./hooks/usePWAUpdate";
import UpdateNotification from "./components/pwa/UpdateNotification";
import OfflineIndicator from "./components/offline/OfflineIndicator";
import QuotesPage from "./pages/QuotesPage";
import QuoteDetailPage from "./pages/Quotes/QuoteDetailPage";
// import AboutPage from "./pages/AboutPage";

function App() {
  const { showUpdatePrompt, applyUpdate, dismissUpdate } = usePWAUpdate();

  // Create a client
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        retry: 1,
        refetchOnWindowFocus: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="App">
          {/* PWA Update Notification */}
          {showUpdatePrompt && (
            <UpdateNotification
              onUpdate={applyUpdate}
              onDismiss={dismissUpdate}
            />
          )}
          {/* Install Prompt */}
          <InstallPrompt />
          <ScrollToTop /> {/* Ensure this is included */}
          <OfflineIndicator /> {/* Offline Status Indicator */}
          <Analytics /> {/* Vercel Analytics */}
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/about" element={<ComingSoon />} />
            {/* <Route path="/q&a" element={<ComingSoon />} /> */}

            {/* Articles */}
            <Route path="/articles" element={<ArticlesListPage />} />
            <Route path="/article/:slug" element={<ArticleDetailPage />} />

            {/* Sermons */}
            <Route path="/sermons" element={<ComingSoon />} />
            <Route path="/sermons/audio" element={<ComingSoon />} />
            <Route path="/sermons/video" element={<ComingSoon />} />

            {/* Sermon audio detail */}
            <Route path="/sermons/audio/:audioId" element={<ComingSoon />} />

            {/* Sermon videos */}
            <Route path="/sermons/video/:videoId" element={<ComingSoon />} />

            {/* Quotes Page */}
            <Route path="/quotes" element={<QuotesPage />} />
            <Route path="/quotes/:id" element={<QuoteDetailPage />} />
            <Route path="/404" element={<NotFoundPage />} />

            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
