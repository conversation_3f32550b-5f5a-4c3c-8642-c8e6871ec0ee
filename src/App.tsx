import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Analytics } from '@vercel/analytics/react';
import HomePage from "./pages/HomePage";
import ComingSoon from "./pages/ComingSoon";
import ArticleDetailPage from "./pages/Articles/ArticleDetailPage";
import ArticlesListPage from "./pages/Articles/ArticlesListPage";
import NotFoundPage from "./pages/NotFoundPage";
import ScrollToTop from "./components/navigation/ScrollToTop";
import InstallPrompt from "./components/pwa/InstallPrompt";
import { usePWAUpdate } from './hooks/usePWAUpdate';
import UpdateNotification from './components/pwa/UpdateNotification';
import OfflineIndicator from "./components/offline/OfflineIndicator";

function App() {
  const { showUpdatePrompt, applyUpdate, dismissUpdate } = usePWAUpdate();

  return (
    <Router>
      <div className="App">
        {/* PWA Update Notification */}
        {showUpdatePrompt && (
          <UpdateNotification
            onUpdate={applyUpdate}
            onDismiss={dismissUpdate}
          />
        )}

        {/* Install Prompt */}
        <InstallPrompt />

        <ScrollToTop /> {/* Ensure this is included */}
        <OfflineIndicator /> {/* Offline Status Indicator */}
        <Analytics /> {/* Vercel Analytics */}
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/about" element={<ComingSoon />} />
          <Route path="/q&a" element={<ComingSoon />} />

          {/* Articles */}
          <Route path="/articles" element={<ArticlesListPage />} />
          <Route path="/article/:slug" element={<ArticleDetailPage />} />

          {/* Sermons */}
          <Route path="/sermons" element={<ComingSoon />} />
          <Route path="/sermons/audio" element={<ComingSoon />} />
          <Route path="/sermons/video" element={<ComingSoon />} />

          {/* Sermon audio detail */}
          <Route path="/sermons/audio/:audioId" element={<ComingSoon />} />

          {/* Sermon videos */}
          <Route path="/sermons/video/:videoId" element={<ComingSoon />} />

          {/* Quotes Page */}
          <Route path="/quotes" element={<ComingSoon />} />
          <Route path="/404" element={<NotFoundPage />} />

          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
