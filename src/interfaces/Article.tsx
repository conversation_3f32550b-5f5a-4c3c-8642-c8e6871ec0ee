export interface Author {
    id: number;
    name: string;
    bio: string;
    profile_picture: string;
}

export interface Category {
    id: number,
    name: string,
    slug: string
}

export interface Article {
    id: number;
    title: string;
    original_title: string;
    slug: string;
    short_description: string;
    content: string;
    featured_image: string;
    categories: Category[];
    original_article_url: string | undefined;
    authors: Author[];
    status: "draft" | "published";
    created_at: string;
    updated_at: string;
}