import axios from "axios";
import { API_ENDPOINTS } from "../config/apiConfig";
import { Article } from "./Article";

// Interface for paginated API response
export interface PaginatedResponse {
    count: number;
    next: string | null;
    previous: string | null;
    results: Article[];
}

// Interface for pagination parameters
export interface PaginationParams {
    page?: number;
    page_size?: number;
}

const fetchArticles = async (params?: PaginationParams): Promise<PaginatedResponse | undefined> => {
    try {
        const queryParams = new URLSearchParams();
        if (params?.page) {
            queryParams.append('page', params.page.toString());
        }
        if (params?.page_size) {
            queryParams.append('page_size', params.page_size.toString());
        }

        const url = `${API_ENDPOINTS.FETCH_ARTICLES}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
        const response = await axios.get(url);
        const articles = response.data;
        // Handle the response here
        return articles; // If you want to return it for further use
    } catch (error) {
        // Handle the error here
        console.error('There was an error fetching articles!', error);
    }
};

// Function to fetch all articles across all pages
export const fetchAllArticles = async (): Promise<Article[]> => {
    try {
        let allArticles: Article[] = [];
        let nextUrl: string | null = API_ENDPOINTS.FETCH_ARTICLES;

        while (nextUrl) {
            const response = await axios.get(nextUrl);
            const data: PaginatedResponse = response.data;

            allArticles = [...allArticles, ...data.results];
            nextUrl = data.next;
        }

        return allArticles;
    } catch (error) {
        console.error('There was an error fetching all articles!', error);
        return [];
    }
};

export const fetchSingleArticle = async (articleSlug: string) => {
    try {
        const response = await axios.get(API_ENDPOINTS.FETCH_SINGLE_ARTICLE(articleSlug));
        const article = response.data;
        // Handle the response here
        return article; // If you want to return it for further use
    } catch (error) {
        // Handle the error here
        console.error('There was an error fetching the article!', error);
    }
};

export default fetchArticles;
