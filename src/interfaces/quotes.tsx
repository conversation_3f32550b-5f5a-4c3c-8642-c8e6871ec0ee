import axios from 'axios';
import { API_ENDPOINTS } from '../config/apiConfig';
import { Quote, QuotesResponse } from './Quote';

export interface FetchQuotesParams {
    page?: number;
    page_size?: number;
    category?: string;
    author?: string;
}

export const fetchQuotes = async (params: FetchQuotesParams = {}): Promise<QuotesResponse> => {
    try {
        const response = await axios.get(API_ENDPOINTS.FETCH_QUOTES, {
            params: {
                page: params.page || 1,
                page_size: params.page_size || 10,
                ...(params.category && { category: params.category }),
                ...(params.author && { author: params.author }),
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching quotes:', error);
        throw error;
    }
};

export const fetchSingleQuote = async (id: string): Promise<Quote> => {
    try {
        const response = await axios.get(API_ENDPOINTS.FETCH_SINGLE_QUOTE(id));
        return response.data;
    } catch (error) {
        console.error('Error fetching single quote:', error);
        throw error;
    }
};

export const fetchRelatedQuotes = async (categories: string[], excludeId?: number): Promise<Quote[]> => {
    try {
        // Fetch all quotes to filter by categories properly
        const response = await axios.get(API_ENDPOINTS.FETCH_QUOTES, {
            params: {
                page_size: 50, // Get more quotes to filter from
            }
        });
        
        const allQuotes = response.data.results;
        
        // Filter quotes that share at least one category with the current quote
        const relatedQuotes = allQuotes.filter((quote: Quote) => {
            // Exclude the current quote
            if (excludeId && quote.id === excludeId) {
                return false;
            }
            
            // If no categories specified, return false
            if (categories.length === 0) {
                return false;
            }
            
            // Check if quote has at least one matching category
            return quote.categories.some(quoteCategory => 
                categories.includes(quoteCategory.name)
            );
        });
        
        // Return up to 6 related quotes
        return relatedQuotes.slice(0, 6);
    } catch (error) {
        console.error('Error fetching related quotes:', error);
        return [];
    }
};
