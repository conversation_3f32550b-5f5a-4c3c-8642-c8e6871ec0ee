import { Link, useParams } from "react-router-dom"; // Import useParams
import NavBar from "../../components/navigation/NavBar";
import { Footer } from "../../components/navigation/footer";
import { Article, Author } from "../../interfaces/Article";
import { fetchSingleArticle, fetchAllArticles } from "../../interfaces/articles";
import { useState, useEffect } from "react";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import SocialShare from "../../components/sharing/SocialShare";
import MetaTags from "../../components/seo/MetaTags";
import { stripHtml } from "../../lib/socialShare";

function ArticleDetailPage() {
    const { slug } = useParams<{ slug: string }>(); // Get slug from URL

    const [detailArticle, setDetailArticle] = useState<Article>();
    const [loading, setLoading] = useState(true);
    const [relatedLoading, setRelatedLoading] = useState(true);

    useEffect(() => {
        const getDetailArticle = async () => {
            if (slug) {
                setLoading(true);
                try {
                    const data = await fetchSingleArticle(slug); // Use slug from URL
                    setDetailArticle(data);


                } catch {
                    // Handle error silently
                } finally {
                    setLoading(false);
                }
            }
        };
        getDetailArticle();
    }, [slug]); // Add slug as a dependency

    const [articles, setArticles] = useState<Article[]>([]);
    const relatedArticles = articles
        .filter(
            (article) =>
                article.slug !== slug && // Exclude the currently viewed article
                article.categories.some((category) =>
                    detailArticle?.categories.some(
                        (detailCategory) => detailCategory.name === category.name
                    )
                )
        )
        .slice(0, 5); // Limit to 5 articles
    useEffect(() => {
        const getArticles = async () => {
            setRelatedLoading(true);
            try {
                // Fetch all articles to get better related articles
                const allArticles = await fetchAllArticles();
                setArticles(allArticles); // Update state with all fetched articles
            } catch {
                // Handle error silently
            } finally {
                setRelatedLoading(false);
            }
        };

        getArticles();
    }, []);

    function formatDate(dateString: string): string {
        const date = new Date(dateString);

        const day = String(date.getDate()).padStart(2, '0');
        const monthShort = date.toLocaleString('en-US', { month: 'short' }); // "Feb"
        const year = date.getFullYear();

        return `${day}. ${monthShort}. ${year}`;
    }

    // Get current URL for sharing
    const currentUrl = window.location.href;

    return (
        <>
            {/* Meta Tags for SEO and Social Sharing */}
            {detailArticle && (
                <MetaTags
                    title={detailArticle.title}
                    description={stripHtml(detailArticle.short_description || detailArticle.content, 160)}
                    url={currentUrl}
                    type="article"
                    author={detailArticle.authors.map(author => author.name).join(', ')}
                    publishedTime={detailArticle.created_at}
                    modifiedTime={detailArticle.updated_at}
                    article={detailArticle}
                />
            )}

            <NavBar />
            <div className="bg-custom-white font-open-sans">
                {loading ? (
                    <>
                        {/* Loading Date */}
                        <div className="px-6 sm:px-10 md:px-16 lg:px-28 pt-6">
                            <Skeleton width={120} height={16} />
                        </div>

                        {/* Loading Title */}
                        <div className="py-6 px-6 sm:px-10 md:px-16 lg:px-28">
                            <Skeleton height={40} width="80%" className="mb-2" />
                            <Skeleton height={40} width="60%" />
                        </div>
                    </>
                ) : (
                    <>
                        {/* Date */}
                        <p className="uppercase px-6 sm:px-10 md:px-16 lg:px-28 pt-6 text-custom-red text-xs font-extralight">
                            {detailArticle && formatDate(detailArticle.created_at)}
                        </p>

                        {/* Title and Share Button */}
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 py-6 px-6 sm:px-10 md:px-16 lg:px-28">
                            <h1 className="text-2xl md:text-3xl font-bold flex-1">
                                {detailArticle?.title}
                            </h1>
                            {detailArticle && (
                                <SocialShare
                                    url={currentUrl}
                                    title={detailArticle.title}
                                    description={stripHtml(detailArticle.short_description || detailArticle.content, 160)}
                                    className="flex-shrink-0"
                                />
                            )}
                        </div>
                    </>
                )}

                {loading ? (
                    <>
                        {/* Loading Featured Image */}
                        <div className="px-6 sm:px-10 md:px-16 lg:px-28">
                            <Skeleton height={400} className="rounded-lg" />
                        </div>

                        {/* Loading Author */}
                        <div className="flex flex-row items-center gap-4 px-6 sm:px-10 md:px-16 lg:px-28 my-5">
                            <Skeleton circle height={60} width={60} />
                            <div>
                                <Skeleton width={120} height={20} className="mb-2" />
                                <Skeleton width={200} height={16} />
                            </div>
                        </div>

                        {/* Loading Content */}
                        <div className="px-6 sm:px-10 md:px-16 lg:px-28 pt-6 space-y-4">
                            <Skeleton height={20} width="100%" />
                            <Skeleton height={20} width="95%" />
                            <Skeleton height={20} width="90%" />
                            <Skeleton height={20} width="85%" />
                            <Skeleton height={20} width="92%" />
                            <Skeleton height={20} width="88%" />
                        </div>
                    </>
                ) : (
                    <>
                        {/* Featured Image */}
                        <img
                            src={detailArticle?.featured_image}
                            alt={detailArticle?.title}
                            id="article-detail-img"
                            className="px-6 sm:px-10 md:px-16 lg:px-28 m-auto w-full max-w-5xl object-cover"
                        />

                        {/* Author */}
                        <div id="authors" className="flex flex-col sm:flex-row ">
                            {detailArticle?.authors.map((author: Author, i) => (
                                <div className="author flex flex-row items-center gap-4 px-6 sm:px-10 md:px-16 lg:px-28 my-5" key={i}>
                                    <img src={author.profile_picture} alt={author.name} className="w-[60px] h-[60px] rounded-full" />
                                    <div className="inline">
                                        <h3 className="text-sm md:text-[15px] font-bold">{author.name}</h3>
                                        <p className="text-sm md:text-[15px] font-extralight">{author.bio}</p>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {/* Content */}
                        <div
                            id="content"
                            className="px-6 sm:px-10 md:px-16 lg:px-28 pt-6 text-base md:text-lg lg:text-xl"
                            dangerouslySetInnerHTML={{ __html: detailArticle?.content || "" }}
                        />
                    </>
                )}

                {/* Translated Version */}
                {loading ? (
                    <div className="px-6 sm:px-10 md:px-16 lg:px-28 pt-6">
                        <Skeleton width={150} height={16} className="mb-4" />
                        <Skeleton width={300} height={24} />
                    </div>
                ) : (
                    <div id="translated-version">
                        <p className="uppercase text-xs font-extralight px-6 sm:px-10 md:px-16 lg:px-28 pt-6 text-custom-red">
                            Verisiyo yahinduwe
                        </p>
                        <a
                            href={detailArticle?.original_article_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-lg md:text-xl hover:underline font-bold py-6 px-6 sm:px-10 md:px-16 lg:px-28 inline-block"
                        >
                            {detailArticle?.original_title}
                        </a>
                    </div>
                )}

                {/* Related Articles */}
                <div className="pt-6">
                    <div className="flex justify-between px-6 sm:px-10 md:px-16 lg:px-28 text-custom-red">
                        <p className="uppercase text-xs font-extralight">Related Articles</p>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 pt-6 px-6 sm:px-10 md:px-16 lg:px-28">
                        {relatedLoading
                            ? Array.from({ length: 3 }).map((_, index) => (
                                <div key={index} className="rounded-md overflow-hidden bg-white p-4">
                                    <Skeleton height={200} className="rounded-md" />
                                    <div className="py-4">
                                        <Skeleton width="80%" height={20} className="my-2" />
                                        <Skeleton width="90%" height={16} className="mb-4" />
                                        <div className="flex items-center gap-3">
                                            <Skeleton circle height={32} width={32} />
                                            <Skeleton width="40%" height={16} />
                                        </div>
                                    </div>
                                </div>
                            ))
                            : relatedArticles.map((rowItem) => (
                                <Link to={`/article/${rowItem.slug}`} className="rounded-md overflow-hidden bg-white shadow-md hover:shadow-lg transition-shadow duration-300" key={rowItem.id}>
                                    <img src={rowItem.featured_image} alt={rowItem.title} className="w-full h-48 object-cover" />
                                    <div className="p-4">
                                        <h1 className="my-4 text-[15px] font-bold">{rowItem.title}</h1>
                                        <p className="mb-4 text-sm line-clamp-3">{rowItem.short_description}</p>
                                        <div className="flex items-center gap-3">
                                            {rowItem.authors.map((author: Author, i) => (
                                                <div className="flex items-center gap-2" key={i}>
                                                    <img className="rounded-full h-8 w-8" src={author.profile_picture} alt={author.name} />
                                                    <p className="text-sm">{author.name}</p>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </Link>
                            ))}
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
}

export default ArticleDetailPage;
