import { useEffect, useState } from "react";
import NavBar from "../../components/navigation/NavBar";
import { Footer } from "../../components/navigation/footer";
import Pagination from "../../components/pagination/Pagination";
import { Article } from "../../interfaces/Article";
import fetchArticles, { fetchAllArticles } from "../../interfaces/articles";
import ArticlesGrid from "../../components/articles/ArticlesGrid";
import SearchBar from "../../components/articles/SearchBar";
import MetaTags from "../../components/seo/MetaTags";

function ArticlesListPage() {
  const [allArticles, setAllArticles] = useState<Article[]>([]);
  const [displayedArticles, setDisplayedArticles] = useState<Article[]>([]);
  const [filteredArticles, setFilteredArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isSearching, setIsSearching] = useState(false);

  const itemsPerPage = 9;

  // Fetch all articles for search functionality
  useEffect(() => {
    const getAllArticles = async () => {
      setLoading(true);
      try {
        const allArticlesData = await fetchAllArticles();
        setAllArticles(allArticlesData);
        setTotalCount(allArticlesData.length);

        // Initially load first page
        const firstPageData = await fetchArticles({ page: 1, page_size: itemsPerPage });
        if (firstPageData) {
          setDisplayedArticles(firstPageData.results);
          setTotalCount(firstPageData.count);
        }
      } catch {
        // Handle error silently
      } finally {
        setLoading(false);
      }
    };

    getAllArticles();
  }, []);

  // Handle pagination for server-side data
  useEffect(() => {
    if (!isSearching) {
      const getPageArticles = async () => {
        setLoading(true);
        try {
          const data = await fetchArticles({ page: currentPage, page_size: itemsPerPage });
          if (data) {
            setDisplayedArticles(data.results);
          }
        } catch {
          // Handle error silently
        } finally {
          setLoading(false);
        }
      };

      getPageArticles();
    }
  }, [currentPage, isSearching]);

  // Handle search filtering (client-side on all articles)
  const handleSearch = (filtered: Article[]) => {
    setFilteredArticles(filtered);
    setIsSearching(filtered.length !== allArticles.length || filtered.length === 0);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Get the articles to display based on search state
  const getArticlesToDisplay = () => {
    if (isSearching) {
      // Client-side pagination for search results
      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      return filteredArticles.slice(startIndex, endIndex);
    }
    // Server-side pagination for normal browsing
    return displayedArticles;
  };

  const getTotalItems = () => {
    return isSearching ? filteredArticles.length : totalCount;
  };

  return (
    <>
      <MetaTags
        title="Inyandiko ngufi - Agakiza n'Ingurane ya Kristo"
        description="Inyandiko ngufi zitandukanye zanditswe na bene Data ku bijyanye n'agakiza, kwicuza, kwizera, n'ubugingo buhoraho. Shakisha no gusoma inyandiko z'Imana mu rurimi rw'ikinyarwanda."
        url={window.location.href}
        type="website"
      />
      <NavBar />
      <div className="bg-custom-white font-open-sans">
        <div className="flex justify-between px-4 sm:px-8 md:px-16 lg:px-28 text-[#B61B3D] pt-6">
          <p className="uppercase">Inyandiko ngufi</p>
        </div>
        <h1 className="text-2xl sm:text-3xl font-bold pt-8 px-4 sm:px-8 md:px-16 lg:px-28">
          Inyandiko ngufi zitandukanye zanditswe na bene Data
        </h1>

        {/* SearchBar Component */}
        <SearchBar articles={allArticles} onSearch={handleSearch} />

        {/* ArticlesGrid Component */}
        <ArticlesGrid articles={getArticlesToDisplay()} loading={loading} />
        <div className="px-4 sm:px-8 md:px-16 lg:px-28 py-8">
          <Pagination
            totalItems={getTotalItems()}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
        </div>
      </div>
      <Footer />
    </>
  );
}

export default ArticlesListPage;
