import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import QuoteCard from '../../components/quotes/QuoteCard';
import NavBar from '../../components/navigation/NavBar';
import { Footer } from '../../components/navigation/footer';
import MetaTags from '../../components/seo/MetaTags';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { FaArrowLeft, FaCalendar, FaUser } from 'react-icons/fa';
import { useQuote, useRelatedQuotes } from '../../hooks/useQuotes';

export default function QuoteDetailPage() {
    const { id } = useParams<{ id: string }>();

    // Use React Query hooks
    const {
        data: quote,
        isLoading: loading,
    } = useQuote(id || '');

    const {
        data: relatedQuotes = [],
        isLoading: relatedLoading
    } = useRelatedQuotes(
        quote?.categories.map(cat => cat.name) || [],
        quote?.id
    );

    if (loading) {
        return (
            <>
                <NavBar />
                <main className="bg-custom-white font-open-sans min-h-screen">
                    <div className="w-full mx-auto px-4 sm:px-8 py-8">
                        {/* Back Button Skeleton */}
                        <div className="mb-8">
                            <Skeleton height={40} width={120} />
                        </div>

                        {/* Quote Detail Skeleton */}
                        <div className="bg-white rounded-2xl p-8 shadow-lg">
                            <Skeleton height={60} className="mb-6" />
                            <Skeleton height={200} className="mb-8" />
                            <Skeleton height={80} className="mb-6" />
                            <div className="flex justify-center gap-4">
                                <Skeleton height={50} width={120} />
                                <Skeleton height={50} width={120} />
                                <Skeleton height={50} width={120} />
                            </div>
                        </div>
                    </div>
                </main>
                <Footer />
            </>
        );
    }

    if (!quote) {
        return (
            <>
                <NavBar />
                <main className="bg-custom-white font-open-sans min-h-screen">
                    <div className="max-w-4xl px-4 sm:px-8 py-8">
                        <div className="text-center">
                            <h1 className="text-2xl font-bold text-gray-800 mb-4">Quote not found</h1>
                            <p className="text-gray-600 mb-8">The quote you're looking for doesn't exist.</p>
                            <Link
                                to="/quotes"
                                className="inline-flex items-center gap-2 px-6 py-3 bg-custom-red text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                                <FaArrowLeft className="w-4 h-4" />
                                Back to Quotes
                            </Link>
                        </div>
                    </div>
                </main>
                <Footer />
            </>
        );
    }

    const currentUrl = `${window.location.origin}/quotes/${quote.id}`;

    return (
        <>
            <MetaTags
                title={`Quote by ${quote.author} - Impano y'Imana`}
                description={quote.text}
                image={quote.author_image}
                url={currentUrl}
                type="article"
                author={quote.author}
                publishedTime={quote.created_at}
                modifiedTime={quote.updated_at}
            />

            <NavBar />
            <main className="bg-custom-white font-open-sans min-h-screen">
                <div className="px-4 sm:px-8 py-8">
                    <div className="max-w-4xl mx-auto">
                        {/* Back Button */}
                        <div className="mb-8">
                            <Link
                                to="/quotes"
                                className="inline-flex items-center gap-2 px-4 py-2 text-custom-red hover:text-red-700 transition-colors"
                            >
                                <FaArrowLeft className="w-4 h-4" />
                                Back to Quotes
                            </Link>
                        </div>

                        {/* Quote Detail */}
                        <div className="mb-12">
                            <QuoteCard quote={quote} variant="detail" />
                        </div>


                        {/* Quote Metadata */}
                        <div className="bg-gray-50 rounded-xl p-6 mb-12">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                                <div className="flex items-center gap-2">
                                    <FaUser className="w-4 h-4 text-custom-red" />
                                    <span>Author: {quote.author}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <FaCalendar className="w-4 h-4 text-custom-red" />
                                    <span>Created: {new Date(quote.created_at).toLocaleDateString()}</span>
                                </div>
                                {quote.categories.length > 0 && (
                                    <div className="flex items-center gap-2">
                                        <span className="text-custom-red font-medium">Categories:</span>
                                        <div className="flex gap-2">
                                            {quote.categories.map(category => (
                                                <span
                                                    key={category.id}
                                                    className="px-2 py-1 bg-custom-red/10 text-custom-red rounded-full text-xs"
                                                >
                                                    {category.name}
                                                </span>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Related Quotes */}
                    {relatedQuotes.length > 0 && (
                        <div className="mb-12 px-4 sm:px-8 md:px-16 lg:px-28">
                            <h2 className="text-2xl font-bold text-gray-800 mb-6">
                                Related Quotes
                            </h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                {relatedQuotes.map(relatedQuote => (
                                    <QuoteCard
                                        key={relatedQuote.id}
                                        quote={relatedQuote}
                                        variant="grid"
                                        onQuoteClick={(quote) => {
                                            window.location.href = `/quotes/${quote.id}`;
                                        }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Related Quotes Loading */}
                    {relatedLoading && (
                        <div className="mb-12">
                            <h2 className="text-2xl font-bold text-gray-800 mb-6">
                                Related Quotes
                            </h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                                {[1, 2, 3].map(i => (
                                    <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden">
                                        <Skeleton height={200} />
                                        <div className="p-6">
                                            <Skeleton height={60} className="mb-4" />
                                            <Skeleton height={40} className="mb-4" />
                                            <div className="flex justify-between">
                                                <Skeleton height={30} width={80} />
                                                <Skeleton height={30} width={60} />
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </main>
            <Footer />
        </>
    );
}
