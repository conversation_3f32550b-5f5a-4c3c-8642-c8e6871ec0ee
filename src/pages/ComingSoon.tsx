import { Link } from "react-router-dom";
import NavBar from "../components/navigation/NavBar";
import { Footer } from "../components/navigation/footer";
import { FaInstagram, FaFacebook, FaYoutube, FaTiktok, FaWhatsapp, FaEnvelope, FaPhone } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { SOCIAL_LINKS } from "../config/socialLinks";

function ComingSoon() {
    return (
        <>
            <NavBar />
            <>
                <div className="flex flex-col items-center justify-center">
                    <div
                        className="absolute h-full w-full inset-0 top-0 left-0 right-0 bottom-0 bg-cover bg-no-repeat bg-center"
                        style={{
                            backgroundImage: "url('/Bible background image.jpeg')",
                            zIndex: -1,
                        }}
                    >
                        <div className="absolute inset-0 bg-black opacity-70"></div>
                    </div>
                    <div className="flex flex-col items-center justify-center text-center px-7 gap-2">
                        <img
                            src="/IMPANO Y'IMANA logo.png"
                            alt="Impano y'Imana Logo"
                            className="h-24 sm:h-32 md:h-40 lg:h-48 mb-4 w-24 sm:w-32 md:w-40 lg:w-48"
                        />
                        <p className="text-[12px] sm:text-[14px] leading-[100%] tracking-normal text-justify uppercase text-[#F1F1F1] font-sans font-normal">
                            The page is under development
                        </p>
                        <h1 className="text-[40px] text-[#ddd] sm:text-[60px] font-open-sans font-semibold leading-[100%] tracking-normal text-justify">
                            Coming <span className="text-custom-red">Soon.</span>
                        </h1>
                        <p className="text-[16px] leading-[100%] tracking-normal text-center text-[#f1f1f1] font-open-sans font-normal sm:text-[16px] my-4">
                            Stay tuned!
                        </p>
                        <Link
                            to="/articles"
                            className="sm:text-lg w-fit px-3 py-1 border rounded-[10px] text-[#f1f1f1] flex items-center justify-center hover:no-underline hover:text-custom-red hover:border-custom-red transition duration-300 ease-in-out"
                        >
                            Check Articles
                        </Link>
                    </div>
                    <hr className="w-9/12 border-t border-[#e5e7eb] sm:border-[0.5px] my-4" />
                    <div id="socials">
                        <div className="flex gap-4 mt-4">
                            <a href={SOCIAL_LINKS.INSTAGRAM} target="_blank" rel="noopener noreferrer">
                                <FaInstagram className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.TWITTER} target="_blank" rel="noopener noreferrer">
                                <FaXTwitter className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.FACEBOOK} target="_blank" rel="noopener noreferrer">
                                <FaFacebook className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.WHATSAPP} target="_blank" rel="noopener noreferrer">
                                <FaWhatsapp className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.PHONE} target="_blank" rel="noopener noreferrer">
                                <FaPhone className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.YOUTUBE} target="_blank" rel="noopener noreferrer">
                                <FaYoutube className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.TIKTOK} target="_blank" rel="noopener noreferrer">
                                <FaTiktok className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.EMAIL} target="_blank" rel="noopener noreferrer">
                                <FaEnvelope className="text-custom-grey min-w-[24px]" />
                            </a>
                        </div>
                    </div>
                </div>
            </>
            <Footer />
        </>
    );
}

export default ComingSoon;
