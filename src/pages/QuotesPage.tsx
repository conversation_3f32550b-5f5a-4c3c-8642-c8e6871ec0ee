import { useState, useMemo } from 'react';
import { Quote } from '../interfaces/Quote';
import QuoteCard from '../components/quotes/QuoteCard';
import QuotesFilter from '../components/quotes/QuotesFilter';
import NavBar from '../components/navigation/NavBar';
import { Footer } from '../components/navigation/footer';
import MetaTags from '../components/seo/MetaTags';
import Pagination from '../components/pagination/Pagination';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';
import { FaQuoteLeft, FaSearch } from 'react-icons/fa';
import { useQuotes, useQuotesMetadata } from '../hooks/useQuotes';

export default function QuotesPage() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedAuthor, setSelectedAuthor] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [, setIsSearching] = useState(false);

    const itemsPerPage = 12;

    // Use React Query hooks

    const { data: quotesData, isLoading, error } = useQuotes({
        page: currentPage,
        page_size: itemsPerPage
    });
    const { authors, categories, isLoading: metadataLoading } = useQuotesMetadata();

    // Filter quotes based on search and filters
    const filteredQuotes = useMemo(() => {
        if (!quotesData?.results) return [];

        let filtered = quotesData.results;

        // Search filter
        if (searchQuery) {
            filtered = filtered.filter(quote =>
                quote.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
                quote.author.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Author filter
        if (selectedAuthor) {
            filtered = filtered.filter(quote => quote.author === selectedAuthor);
        }

        // Category filter
        if (selectedCategory) {
            filtered = filtered.filter(quote =>
                quote.categories.some(cat => cat.name === selectedCategory)
            );
        }

        return filtered;
    }, [quotesData?.results, searchQuery, selectedAuthor, selectedCategory]);

    // Handle search and filter changes
    const handleSearch = (query: string) => {
        setSearchQuery(query);
        setIsSearching(!!query || !!selectedAuthor || !!selectedCategory);
        setCurrentPage(1);
    };

    const handleFilterByAuthor = (author: string) => {
        setSelectedAuthor(author);
        setIsSearching(!!searchQuery || !!author || !!selectedCategory);
        setCurrentPage(1);
    };

    const handleFilterByCategory = (category: string) => {
        setSelectedCategory(category);
        setIsSearching(!!searchQuery || !!selectedAuthor || !!category);
        setCurrentPage(1);
    };

    const handleClearFilters = () => {
        setSearchQuery('');
        setSelectedAuthor('');
        setSelectedCategory('');
        setIsSearching(false);
        setCurrentPage(1);
    };

    const loading = isLoading || metadataLoading;

    const handleQuoteClick = (quote: Quote) => {
        window.location.href = `/quotes/${quote.id}`;
    };

    return (
        <>
            <MetaTags
                title="Amagambo yavuzwe - Impano y'Imana"
                description="Soma amagambo yavuzwe na bene Data babaye abizerwa. Inyandiko ngufi zitandukanye zanditswe na bene Data ku bijyanye n'agakiza, ingurane, n'urukundo rw'Imana."
                image="/ISEZERANO LOGO copy - rm bg.png"
                url={window.location.href}
                type="website"
            />

            <NavBar />
            <main className="bg-gray-100 font-open-sans min-h-screen">
                {/* Search Bar Section */}
                <div className="p-6 px-4 sm:px-8 md:px-16 lg:px-28">
                    <QuotesFilter
                        onSearch={handleSearch}
                        onFilterByAuthor={handleFilterByAuthor}
                        onFilterByCategory={handleFilterByCategory}
                        onClearFilters={handleClearFilters}
                        authors={authors}
                        categories={categories}
                        loading={loading}
                    />
                </div>

                {/* Hero Section */}
                <div className="relative overflow-hidden bg-gradient-to-br from-custom-red/5 via-white to-custom-red/5 mb-8">
                    {/* Decorative Elements */}
                    <div className="absolute inset-0">
                        <div className="absolute top-8 left-8 w-24 h-24 bg-custom-red/10 rounded-full blur-2xl"></div>
                        <div className="absolute bottom-8 right-8 w-32 h-32 bg-custom-red/8 rounded-full blur-3xl"></div>
                        <div className="absolute top-1/2 left-1/3 w-16 h-16 bg-custom-red/6 rounded-full blur-xl"></div>
                    </div>

                    <div className="relative w-full py-8 md:py-12">
                        <div className="text-center">
                            {/* Enhanced Icon with Logo */}
                            <div className="flex justify-center mb-6">
                                <div className="relative group">
                                    <div className="w-20 h-20 bg-gradient-to-br from-custom-red to-red-700 flex items-center justify-center shadow-2xl transform group-hover:scale-110 group-hover:rotate-2 transition-all duration-500">
                                        <FaQuoteLeft className="w-10 h-10 text-white" />
                                    </div>
                                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                                        <div className="w-3 h-3 bg-yellow-600"></div>
                                    </div>
                                    {/* Glow effect */}
                                    <div className="absolute inset-0 bg-custom-red/20 blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                                </div>
                            </div>

                            {/* Enhanced Title */}
                            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-custom-red to-gray-900 bg-clip-text text-transparent leading-tight mb-4">
                                Amagambo yavuzwe
                            </h1>

                            {/* Enhanced Description */}
                            <div className="max-w-3xl mx-auto">
                                <p className="text-lg sm:text-xl text-gray-700 leading-relaxed mb-6 px-4">
                                    Igira kuri bene Data babaye abizerwa. Soma amagambo yavuzwe na bene Data babaye abizerwa ku bijyanye n'
                                    <span className="text-custom-red font-bold bg-custom-red/10 px-2 py-1 rounded-lg">agakiza</span>,
                                    <span className="text-custom-red font-bold bg-custom-red/10 px-2 py-1 rounded-lg mx-1">ingurane</span>, n'
                                    <span className="text-custom-red font-bold bg-custom-red/10 px-2 py-1 rounded-lg">urukundo rw'Imana</span>.
                                </p>

                                {/* Enhanced Stats */}
                                <div className="flex flex-wrap justify-center items-center gap-6 sm:gap-8 text-sm text-gray-600">
                                    <div className="flex items-center space-x-3 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm border border-custom-red/10">
                                        <div className="w-2 h-2 bg-custom-red rounded-full animate-pulse"></div>
                                        <span className="font-medium">Amagambo yavuzwe</span>
                                    </div>
                                    <div className="flex items-center space-x-3 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm border border-custom-red/10">
                                        <div className="w-2 h-2 bg-custom-red rounded-full animate-pulse"></div>
                                        <span className="font-medium">Bene Data babaye abizerwa</span>
                                    </div>
                                    <div className="flex items-center space-x-3 bg-white/70 backdrop-blur-sm px-4 py-2 rounded-full shadow-sm border border-custom-red/10">
                                        <div className="w-2 h-2 bg-custom-red rounded-full animate-pulse"></div>
                                        <span className="font-medium">Inyandiko ngufi</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Content Section */}
                <div className="w-full px-4 sm:px-8 md:px-16 lg:px-28">

                    {/* Error State */}
                    {error && (
                        <div className="text-center py-8">
                            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <FaSearch className="w-10 h-10 text-red-500" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 mb-2">
                                Error loading quotes
                            </h3>
                            <p className="text-gray-600 mb-4">
                                There was an error loading the quotes. Please try again later.
                            </p>
                            <button
                                onClick={() => window.location.reload()}
                                className="px-6 py-3 bg-custom-red text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                                Retry
                            </button>
                        </div>
                    )}

                    {/* Results Count */}
                    {!error && (
                        <div className="mb-4">
                            <p className="text-gray-600 text-sm">
                                {loading ? (
                                    <Skeleton width={150} />
                                ) : (
                                    `Found ${quotesData?.count || 0} quote${(quotesData?.count || 0) !== 1 ? 's' : ''}`
                                )}
                            </p>
                        </div>
                    )}

                    {/* Quotes Grid */}
                    {!error && (loading ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6">
                            {[1, 2, 3, 4, 5, 6].map(i => (
                                <div key={i} className="bg-white rounded-xl shadow-md overflow-hidden">
                                    <div className="flex">
                                        <Skeleton className="w-2/5 min-w-[140px] max-w-[200px]" height={120} />
                                        <div className="flex-1 p-4">
                                            <Skeleton height={40} className="mb-2" />
                                            <Skeleton height={20} className="mb-2" />
                                            <Skeleton height={15} className="mb-3" />
                                            <div className="flex justify-start">
                                                <Skeleton height={25} width={70} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : filteredQuotes.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
                            {filteredQuotes.map(quote => (
                                <QuoteCard
                                    key={quote.id}
                                    quote={quote}
                                    variant="grid"
                                    onQuoteClick={handleQuoteClick}
                                />
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-8">
                            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <FaSearch className="w-10 h-10 text-gray-400" />
                            </div>
                            <h3 className="text-lg font-semibold text-gray-800 mb-2">
                                No quotes found
                            </h3>
                            <p className="text-gray-600 mb-4">
                                Try adjusting your search or filters to find what you're looking for.
                            </p>
                            <button
                                onClick={handleClearFilters}
                                className="px-6 py-3 bg-custom-red text-white rounded-lg hover:bg-red-700 transition-colors"
                            >
                                Clear All Filters
                            </button>
                        </div>
                    ))}

                    {/* Pagination */}
                    {!error && !loading && quotesData && quotesData.count > itemsPerPage && (
                        <div className="mt-8">
                            <Pagination
                                totalItems={quotesData.count}
                                itemsPerPage={itemsPerPage}
                                currentPage={currentPage}
                                onPageChange={setCurrentPage}
                            />
                        </div>
                    )}
                </div>
            </main>
            <Footer />
        </>
    );
}