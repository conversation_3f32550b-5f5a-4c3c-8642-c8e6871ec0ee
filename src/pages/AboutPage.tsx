import NavBar from "../components/navigation/NavBar";
import { Footer } from "../components/navigation/footer";
import MetaTags from '../components/seo/MetaTags';
import { <PERSON>aBook, <PERSON>aH<PERSON>t, Fa<PERSON>sers, FaChurch, FaQuoteLeft, FaPlay } from 'react-icons/fa';

function AboutPage() {
    return (
        <>
            <MetaTags
                title="About Us - Impano y'Imana"
                description="Learn about Impano y'Imana, our mission, vision, and the team dedicated to sharing <PERSON>'s word through articles, sermons, and quotes."
                image="/ISEZERANO LOGO copy - rm bg.png"
                url={window.location.href}
                type="website"
            />
            
            <NavBar />
            
            <main className="bg-white font-open-sans">
                {/* Hero Section */}
                <section className="relative overflow-hidden bg-gradient-to-br from-custom-red/5 via-white to-custom-red/5">
                    {/* Decorative Elements */}
                    <div className="absolute inset-0">
                        <div className="absolute top-20 left-20 w-32 h-32 bg-custom-red/5 rounded-full blur-3xl animate-pulse"></div>
                        <div className="absolute bottom-20 right-20 w-40 h-40 bg-custom-red/3 rounded-full blur-3xl animate-pulse delay-1000"></div>
                        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-custom-red/4 rounded-full blur-2xl animate-pulse delay-500"></div>
                    </div>
                    
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24 relative z-10">
                        <div className="text-center">
                            {/* Subtitle */}
                            <div className="inline-flex items-center px-6 py-3 bg-custom-red/10 rounded-full text-custom-red font-semibold text-sm uppercase tracking-wide mb-8 border border-custom-red/20 hover:bg-custom-red/20 transition-all duration-300">
                                <FaHeart className="w-4 h-4 mr-2 animate-pulse" />
                                WHO ARE WE?
                            </div>

                            {/* Main Heading */}
                            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-8">
                                <span className="text-custom-red bg-gradient-to-r from-custom-red to-red-600 bg-clip-text text-transparent">Impano</span> y'Imana
                                <br />
                                <span className="text-2xl sm:text-3xl lg:text-4xl text-gray-600 font-normal">
                                    Sharing God's Word with the World
                                </span>
                            </h1>

                            {/* Description */}
                            <div className="max-w-4xl mx-auto mb-12">
                                <p className="text-lg sm:text-xl text-gray-700 leading-relaxed mb-8">
                                    We are a community dedicated to spreading the Gospel through digital platforms, 
                                    providing accessible resources for spiritual growth, and building a global 
                                    fellowship of believers united in faith and purpose.
                                </p>
                                <p className="text-base text-gray-600 leading-relaxed">
                                    Our mission is to make God's word accessible to everyone, everywhere, 
                                    through articles, sermons, quotes, and interactive content that inspires, 
                                    educates, and strengthens the faith of believers worldwide.
                                </p>
                            </div>
                        </div>

                        {/* Full Width Hero Image */}
                        <div className="relative group">
                            <div className="absolute inset-0 bg-custom-red/10 rounded-3xl blur-3xl transform group-hover:scale-105 transition-transform duration-700"></div>
                            <div className="relative bg-white p-2 sm:p-4 rounded-3xl shadow-2xl">
                                <img 
                                    src="/bible.png" 
                                    alt="Open Bible with divine light" 
                                    className="w-full h-64 sm:h-80 md:h-96 lg:h-[500px] object-cover rounded-2xl shadow-lg"
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent rounded-2xl"></div>
                                
                                {/* Overlay Text */}
                                <div className="absolute bottom-4 sm:bottom-8 left-4 sm:left-8 right-4 sm:right-8 text-center">
                                    <h3 className="text-lg sm:text-2xl md:text-3xl font-bold text-white mb-2">
                                        "Your word is a lamp to my feet and a light to my path"
                                    </h3>
                                    <p className="text-white/90 text-sm sm:text-lg">Psalm 119:105</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* What We Believe Section */}
                <section className="py-16 lg:py-24 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <div className="inline-flex items-center px-4 py-2 bg-custom-red/10 rounded-full text-custom-red font-semibold text-sm uppercase tracking-wide mb-6">
                                <FaChurch className="w-4 h-4 mr-2" />
                                WHAT DO WE BELIEVE IN?
                            </div>
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-8">
                                The <span className="text-custom-red">1689</span> London Baptist 
                                <span className="text-custom-red"> Confession of Faith</span>
                            </h2>
                        </div>

                        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
                            {/* Left Column - Image */}
                            <div className="relative group">
                                <div className="absolute inset-0 bg-custom-red/10 rounded-3xl transform rotate-3 group-hover:rotate-6 transition-transform duration-500"></div>
                                <div className="relative bg-white p-4 sm:p-6 rounded-3xl shadow-xl">
                                    <img 
                                        src="/catechism.png" 
                                        alt="1689 London Baptist Confession" 
                                        className="w-full h-64 sm:h-80 object-cover rounded-2xl"
                                    />
                                    {/* Overlay with Confession Title */}
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent rounded-2xl flex items-end">
                                        <div className="p-4 sm:p-6 text-white">
                                            <h3 className="text-xl sm:text-2xl font-bold mb-2">THE 1689</h3>
                                            <h4 className="text-base sm:text-lg font-semibold mb-1">LONDON BAPTIST</h4>
                                            <h4 className="text-base sm:text-lg font-semibold mb-2">CONFESSION</h4>
                                            <p className="text-xs sm:text-sm opacity-90">OF FAITH</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Right Column - Content */}
                            <div className="space-y-6">
                                <div className="bg-gray-50 p-6 rounded-2xl">
                                    <h3 className="text-xl font-bold text-gray-900 mb-4">Our Foundation</h3>
                                    <p className="text-gray-700 leading-relaxed">
                                        Founded on the solid rock of Scripture, we adhere to the 1689 London Baptist 
                                        Confession of Faith, which provides a comprehensive statement of our beliefs 
                                        and serves as our doctrinal foundation.
                                    </p>
                                </div>
                                
                                <div className="bg-custom-red/5 p-6 rounded-2xl border-l-4 border-custom-red">
                                    <h3 className="text-xl font-bold text-gray-900 mb-4">Core Beliefs</h3>
                                    <p className="text-gray-700 leading-relaxed">
                                        We believe in one holy, catholic, and apostolic Church, the authority of 
                                        Scripture, the sovereignty of God, and the salvation by grace through 
                                        faith in Jesus Christ alone.
                                    </p>
                                </div>

                                <div className="flex flex-wrap gap-3">
                                    <span className="px-4 py-2 bg-custom-red text-white rounded-full text-sm font-medium">
                                        Sola Scriptura
                                    </span>
                                    <span className="px-4 py-2 bg-custom-red text-white rounded-full text-sm font-medium">
                                        Sola Fide
                                    </span>
                                    <span className="px-4 py-2 bg-custom-red text-white rounded-full text-sm font-medium">
                                        Sola Gratia
                                    </span>
                                    <span className="px-4 py-2 bg-custom-red text-white rounded-full text-sm font-medium">
                                        Solus Christus
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Mission & Vision Section */}
                <section className="relative py-16 lg:py-24 overflow-hidden">
                    {/* Background Image */}
                    <div 
                        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                        style={{
                            backgroundImage: `url('/brethren.png')`,
                            filter: 'brightness(0.4)'
                        }}
                    ></div>
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-r from-black/85 via-black/70 to-black/85"></div>
                    
                    <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white font-semibold text-sm uppercase tracking-wide mb-6">
                                <FaUsers className="w-4 h-4 mr-2" />
                                MISSION & VISION
                            </div>
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8">
                                Our Purpose & <span className="text-yellow-400">Future</span>
                            </h2>
                        </div>

                        <div className="grid md:grid-cols-2 gap-8">
                            {/* Mission */}
                            <div className="group bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105">
                                <div className="flex items-center mb-6">
                                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4 group-hover:bg-white/30 transition-all duration-300">
                                        {/* Cross Icon */}
                                        <svg className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold text-white group-hover:text-yellow-300 transition-colors duration-300">Mission</h3>
                                </div>
                                <p className="text-white/90 leading-relaxed text-lg group-hover:text-white transition-colors duration-300">
                                    To make God's word accessible to everyone through digital platforms, 
                                    providing resources that inspire spiritual growth, strengthen faith, 
                                    and build a global community of believers united in Christ.
                                </p>
                            </div>

                            {/* Vision */}
                            <div className="group bg-white/10 backdrop-blur-lg rounded-3xl p-8 border border-white/20 hover:bg-white/15 hover:border-white/30 transition-all duration-500 transform hover:scale-105">
                                <div className="flex items-center mb-6">
                                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-4 group-hover:bg-white/30 transition-all duration-300">
                                        {/* Cross Icon */}
                                        <svg className="w-6 h-6 text-white group-hover:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold text-white group-hover:text-yellow-300 transition-colors duration-300">Vision</h3>
                                </div>
                                <p className="text-white/90 leading-relaxed text-lg group-hover:text-white transition-colors duration-300">
                                    To become the leading digital platform for Christian resources, 
                                    reaching millions worldwide with transformative content that 
                                    deepens understanding of Scripture and strengthens the global Church.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Leadership Section */}
                <section className="py-16 lg:py-24 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <div className="inline-flex items-center px-4 py-2 bg-custom-red/10 rounded-full text-custom-red font-semibold text-sm uppercase tracking-wide mb-6">
                                <FaUsers className="w-4 h-4 mr-2" />
                                LEADERSHIP
                            </div>
                            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-8">
                                Meet our <span className="text-custom-red">leadership team</span>
                            </h2>
                            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                                Our dedicated leaders guide the vision and direction of Impano y'Imana, 
                                ensuring that our mission to spread God's word remains at the heart of everything we do.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
                            {/* Leadership Cards */}
                            {[
                                {
                                    name: "Rev. John Calvin",
                                    role: "Founder & Lead Pastor",
                                    image: "/Giclee Print_ John Calvin, (1509-1564) _ 12x9in.jpeg"
                                },
                                {
                                    name: "Dr. Martin Luther",
                                    role: "Theological Advisor",
                                    image: "/John Piper_ Christians Must Prepare for the End Times Now.jpeg"
                                },
                                {
                                    name: "Rev. Jonathan Edwards",
                                    role: "Content Director",
                                    image: "/Jonathan Edwards.jpeg"
                                },
                                {
                                    name: "Dr. Paul Washer",
                                    role: "Outreach Coordinator",
                                    image: "/The KJV Removed verses.jpeg"
                                }
                            ].map((leader, index) => (
                                <div key={index} className="group">
                                    <div className="bg-white rounded-2xl shadow-lg overflow-hidden transform group-hover:scale-105 transition-all duration-300">
                                        <div className="relative">
                                            <img 
                                                src={leader.image} 
                                                alt={leader.name}
                                                className="w-full h-48 sm:h-56 lg:h-64 object-cover"
                                            />
                                            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                                            <div className="absolute bottom-3 sm:bottom-4 left-3 sm:left-4 right-3 sm:right-4">
                                                <h3 className="text-white font-bold text-base sm:text-lg">{leader.name}</h3>
                                                <p className="text-white/90 text-xs sm:text-sm">{leader.role}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Values Section */}
                <section className="py-16 lg:py-24 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-8">
                                Our <span className="text-custom-red">Core Values</span>
                            </h2>
                        </div>

                        <div className="grid md:grid-cols-3 gap-8">
                            {[
                                {
                                    icon: <FaBook className="w-8 h-8" />,
                                    title: "Biblical Authority",
                                    description: "We hold Scripture as the ultimate authority in all matters of faith and practice."
                                },
                                {
                                    icon: <FaHeart className="w-8 h-8" />,
                                    title: "Love & Compassion",
                                    description: "We demonstrate Christ's love through our actions and care for others."
                                },
                                {
                                    icon: <FaUsers className="w-8 h-8" />,
                                    title: "Community",
                                    description: "We believe in the power of fellowship and building strong Christian communities."
                                }
                            ].map((value, index) => (
                                <div key={index} className="group text-center p-8 bg-gray-50 rounded-2xl hover:bg-custom-red/5 transition-all duration-500 transform hover:scale-105 hover:shadow-xl border border-transparent hover:border-custom-red/20">
                                    <div className="w-16 h-16 bg-custom-red text-white rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-red-600 group-hover:scale-110 transition-all duration-300 shadow-lg">
                                        {value.icon}
                                    </div>
                                    <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-custom-red transition-colors duration-300">{value.title}</h3>
                                    <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">{value.description}</p>
                                </div>
                            ))}
                        </div>
                    </div>
                </section>

                {/* Call to Action */}
                <section className="py-16 lg:py-24 bg-gray-900">
                    <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
                        <h2 className="text-3xl sm:text-4xl font-bold text-white mb-6">
                            Join Our Community
                        </h2>
                        <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                            Be part of a growing community dedicated to spreading God's word 
                            and strengthening the faith of believers worldwide.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-6 justify-center">
                            <button className="group px-8 py-4 bg-custom-red text-white font-bold rounded-xl hover:bg-red-600 transition-all duration-300 flex items-center justify-center transform hover:scale-105 hover:shadow-xl">
                                <FaQuoteLeft className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" />
                                Read Our Quotes
                            </button>
                            <button className="group px-8 py-4 border-2 border-gray-600 text-gray-300 font-bold rounded-xl hover:bg-gray-600 hover:text-white transition-all duration-300 flex items-center justify-center transform hover:scale-105 hover:shadow-xl">
                                <FaPlay className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                                Watch Sermons
                            </button>
                        </div>
                    </div>
                </section>
            </main>
            
            <Footer />
        </>
    );
}

export default AboutPage;
