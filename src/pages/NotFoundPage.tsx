import React from 'react';
import { Link } from 'react-router-dom';
import NavBar from '../components/navigation/NavBar';
import { Footer } from '../components/navigation/footer';
import { FaInstagram, FaFacebook, FaWhatsapp, FaPhone, FaYoutube, FaTiktok, FaEnvelope } from 'react-icons/fa';
import { FaXTwitter } from 'react-icons/fa6';
import { SOCIAL_LINKS } from '../config/socialLinks';

const NotFoundPage: React.FC = () => {
    return (
        <>
            <NavBar />
            <div className="">
                <div
                    className="absolute h-full w-full inset-0 top-0 left-0 right-0 bottom-0 bg-cover bg-no-repeat bg-center"
                    style={{
                        backgroundImage: "url('/The KJV Removed verses.jpeg')",
                        transform: "scaleX(-1)",
                        zIndex: -1,
                    }}
                >
                    <div className="absolute inset-0 bg-black opacity-70"></div>
                </div>
                <div className="flex flex-col items-center justify-center text-center px-7 gap-2 w-9/12 mx-auto">
                    <div className="flex flex-col items-left justify-left text-left p-7 px-0 gap-2 my-7 mr-auto">
                        <p className="text-[12px] sm:text-[14px] leading-[100%] tracking-normal text-justify uppercase text-[#F1F1F1] font-sans font-normal">
                            Page not found
                        </p>
                        <h1 className="text-[40px] text-[#ddd] sm:text-[60px] font-open-sans font-semibold leading-[100%] tracking-normal text-justify">
                            4<span className="text-custom-red">0</span>4
                        </h1>
                        <p className="text-[16px] leading-[100%] tracking-normal text-left text-[#f1f1f1] font-open-sans font-normal sm:text-[16px] my-4">
                            Sorry, the page you are looking for does not exist. <br />
                            It might have been moved or deleted.
                        </p>
                        <Link
                            to="/"
                            className="sm:text-lg w-fit px-3 py-1 border rounded-[10px] text-[#f1f1f1] flex items-left justify-center hover:no-underline hover:text-custom-red hover:border-custom-red transition duration-300 ease-in-out"
                        >
                            Go to Home
                        </Link>
                    </div>
                    <hr className="w-full border-t border-[#e5e7eb] sm:border-[0.5px] my-4" />
                    <div id="socials" className=''>
                        <div className="flex gap-4 mt-4 justify-center">
                            <a href={SOCIAL_LINKS.INSTAGRAM} target="_blank" rel="noopener noreferrer">
                                <FaInstagram className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.TWITTER} target="_blank" rel="noopener noreferrer">
                                <FaXTwitter className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.FACEBOOK} target="_blank" rel="noopener noreferrer">
                                <FaFacebook className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.WHATSAPP} target="_blank" rel="noopener noreferrer">
                                <FaWhatsapp className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.PHONE} target="_blank" rel="noopener noreferrer">
                                <FaPhone className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.YOUTUBE} target="_blank" rel="noopener noreferrer">
                                <FaYoutube className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.TIKTOK} target="_blank" rel="noopener noreferrer">
                                <FaTiktok className="text-custom-grey min-w-[24px]" />
                            </a>
                            <a href={SOCIAL_LINKS.EMAIL} target="_blank" rel="noopener noreferrer">
                                <FaEnvelope className="text-custom-grey min-w-[24px]" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <Footer />
        </>
    );
};

export default NotFoundPage;