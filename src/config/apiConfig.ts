import { ENV_CONFIG, validateEnvironment } from './environment';

// Validate environment configuration
validateEnvironment();

const BASE_URL = ENV_CONFIG.API_BASE_URL;

export const API_ENDPOINTS = {
    // Subscription endpoint
    SUBSCRIBE: `${BASE_URL}/subscriptions/subscribe/`,

    // Articles endpoints
    FETCH_ARTICLES: `${BASE_URL}/blog-posts/`,
    FETCH_SINGLE_ARTICLE: (slug: string) => `${BASE_URL}/blog-posts/${slug}/`,

    // Sermons endpoints
    FETCH_SERMONS: `${BASE_URL}/sermons/`,
    FETCH_SINGLE_SERMON: (id: string) => `${BASE_URL}/sermons/${id}/`,

    // Quotes endpoints
    FETCH_QUOTES: `${BASE_URL}/quotes/`,
    FETCH_SINGLE_QUOTE: (id: string) => `${BASE_URL}/quotes/${id}/`,

    // Categories endpoints
    FETCH_CATEGORIES: `${BASE_URL}/categories/`,
    FETCH_CATEGORY_ARTICLES: (categoryId: string) => `${BASE_URL}/categories/${categoryId}/blog-posts/`,

    // User endpoints
    LOGIN: `${BASE_URL}/auth/login/`,
    REGISTER: `${BASE_URL}/auth/register/`,
    PROFILE: `${BASE_URL}/auth/profile/`,
};