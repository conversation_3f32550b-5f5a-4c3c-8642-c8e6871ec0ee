export const SITE_CONFIG = {
  name: "Impano y'Imana Ministry",
  url: 'https://impanoyimana.org',
  defaultImage: '/ISEZERANO LOGO copy - rm bg.png', // Use transparent background logo
  socialShareImage: '/ISEZERANO LOGO copy - rm bg.png', // Will be replaced with black background version
  favicon: '/ISEZERANO LOGO copy - rm bg.png',
  description: "Inyandiko ngufi zitandukanye zanditswe na bene Data. Soma inyandiko z'Imana zitandukanye mu rurimi rw'ikinyarwanda.",
  keywords: [
    'agakiza', 'gukiranuka', 'icyaha', 'Yesu', 'Kristo', 'ingurane', 'ubutungane',
    'kurimbuka', 'umukiranutsi', 'umunyabyaha', 'amaraso', 'ubwoba bw\'Imana',
    'kwicuza', 'kwizera', 'ubugingo', 'ubwiyunge', 'guhinduka', 'ub<PERSON> bw\'Imana',
    'ubu<PERSON><PERSON>mugayo', 'urukundo', 'amahoro', 'ubushake bw\'Imana', 'gusaba',
    'gusengera', 'ubugingo buhoraho', 'Impano y\'Imana', 'Ministry', 'Articles',
    'Sermons', 'Christian', 'Rwanda', 'Kinyarwanda', 'inyandiko', 'ibyigisho',
    'Imana', 'Uwiteka', 'Bibiliya'
  ],
  social: {
    twitter: '@impanoyimana1',
    facebook: 'impanoyimana',
  },
  themeColor: '#B61B3D'
};

export const getDefaultHashtags = (type: 'article' | 'sermon' | 'general' = 'article') => {
  const commonTags = ['ImpanoyImana', 'Ministry', 'Christian', 'Rwanda'];
  
  switch (type) {
    case 'article':
      return [...commonTags, 'Inyandiko', 'Faith', 'Bibiliya'];
    case 'sermon':
      return [...commonTags, 'Sermon', 'Preaching', 'Gospel'];
    default:
      return commonTags;
  }
};
